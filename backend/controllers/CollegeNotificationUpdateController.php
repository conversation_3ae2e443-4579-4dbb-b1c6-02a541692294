<?php

namespace backend\controllers;

use Yii;
use common\models\CollegeNotificationUpdate;
use common\models\CollegeNotificationUpdateSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * CollegeNotificationUpdateController implements the CRUD actions for CollegeNotificationUpdate model.
 */
class CollegeNotificationUpdateController extends Controller
{
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all CollegeNotificationUpdate models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new CollegeNotificationUpdateSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single CollegeNotificationUpdate model.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new CollegeNotificationUpdate model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new CollegeNotificationUpdate();

        if ($model->load(Yii::$app->request->post())) {
            if ($model->status == 1) {
                $model->publish_at = new \yii\db\Expression('NOW()');
            }

            if ($model->save()) {
                return $this->redirect(['view', 'id' => $model->id]);
            }
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * Bulk upload CollegeNotificationUpdate models via CSV.
     * @return mixed
     */
    public function actionBulkUpload()
    {
        $model = new CollegeNotificationUpdate();

        if (isset($_FILES['csv_upload'])) {
            $filePath = $_FILES['csv_upload']['tmp_name'];
        }

        if (!empty($filePath) && file_exists($filePath)) {
            // MIME Check
            $csvMimes = ['text/x-comma-separated-values', 'text/comma-separated-values', 'application/octet-stream', 'application/vnd.ms-excel', 'application/x-csv', 'text/x-csv', 'text/csv', 'application/csv', 'application/excel', 'application/vnd.msexcel', 'text/plain'];

            $finfo = finfo_open(FILEINFO_MIME_TYPE);
            $mime = finfo_file($finfo, $filePath);
            finfo_close($finfo);

            if (in_array($mime, $csvMimes) === false) {
                Yii::$app->session->setFlash('error', 'Invalid CSV File - Mime Type Mismatch');
                return $this->redirect('bulk-upload');
            }

            $csvFile = fopen($filePath, 'r');
            $row = 1;
            $successCount = 0;
            $errorCount = 0;

            while (($data = fgetcsv($csvFile, 1000, ',')) !== false) {
                if ($row > 1) { // Skip header row
                    $newModel = new CollegeNotificationUpdate();

                    // Map CSV columns to model attributes
                    // Expected CSV format: college_id, text, sub_page, content, status, start_date, end_date, publish_at
                    if (isset($data[0]) && !empty($data[0])) {
                        $newModel->college_id = (int)$data[0];
                    }

                    if (isset($data[1]) && !empty($data[1])) {
                        $newModel->text = $data[1];
                    }

                    if (isset($data[2]) && !empty($data[2])) {
                        $newModel->sub_page = $data[2];
                    }

                    if (isset($data[3]) && !empty($data[3])) {
                        $newModel->content = $data[3];
                    }

                    if (isset($data[4]) && !empty($data[4])) {
                        $newModel->status = (int)$data[4];
                    } else {
                        $newModel->status = CollegeNotificationUpdate::STATUS_INACTIVE;
                    }

                    if (isset($data[5]) && !empty($data[5])) {
                        $newModel->start_date = $data[5];
                    }

                    if (isset($data[6]) && !empty($data[6])) {
                        $newModel->end_date = $data[6];
                    }

                    if (isset($data[7]) && !empty($data[7])) {
                        $newModel->publish_at = $data[7];
                    } elseif ($newModel->status == CollegeNotificationUpdate::STATUS_ACTIVE) {
                        $newModel->publish_at = new \yii\db\Expression('NOW()');
                    }

                    if ($newModel->save()) {
                        $successCount++;
                    } else {
                        $errorCount++;
                        $errors = implode(', ', array_map(function($error) {
                            return is_array($error) ? implode(', ', $error) : $error;
                        }, $newModel->errors));
                        Yii::error("CSV Import failed at line {$row}: {$errors}");
                    }
                }
                $row++;
            }

            fclose($csvFile);

            if ($errorCount > 0) {
                Yii::$app->session->setFlash('warning', "CSV Import completed with {$successCount} successful records and {$errorCount} failed records.");
            } else {
                Yii::$app->session->setFlash('success', "CSV Import was successful. {$successCount} records imported.");
            }

            return $this->redirect('index');
        }

        return $this->render('bulk-upload', [
            'model' => $model,
        ]);
    }

    /**
     * Updates an existing CollegeNotificationUpdate model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing CollegeNotificationUpdate model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the CollegeNotificationUpdate model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return CollegeNotificationUpdate the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = CollegeNotificationUpdate::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
