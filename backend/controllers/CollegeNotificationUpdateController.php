<?php

namespace backend\controllers;

use common\helpers\ContentHelper;
use Yii;
use common\models\CollegeNotificationUpdate;
use common\models\CollegeNotificationUpdateSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * CollegeNotificationUpdateController implements the CRUD actions for CollegeNotificationUpdate model.
 */
class CollegeNotificationUpdateController extends Controller
{
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all CollegeNotificationUpdate models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new CollegeNotificationUpdateSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single CollegeNotificationUpdate model.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new CollegeNotificationUpdate model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        dd("hi");
        $model = new CollegeNotificationUpdate();
        $postData = Yii::$app->request->post();
        $uploadMode = Yii::$app->request->post('upload_mode', 'single');

        // Debug information
        Yii::$app->session->setFlash('info', "Create action called. Upload mode: " . $uploadMode . " | Files: " . (isset($_FILES['bulk_csv']) ? 'Yes' : 'No'));

        // Check if this is a bulk upload first (before model validation)
        if ($uploadMode === 'bulk' && isset($_FILES['bulk_csv']) && !empty($_FILES['bulk_csv']['tmp_name'])) {
            // For bulk upload, we only need the common fields, not college_id/sub_page
            $tempModel = new CollegeNotificationUpdate();
            $tempModel->load($postData);
            return $this->processBulkUploadFromForm($tempModel, $_FILES['bulk_csv']);
        }

        // Debug: Check if bulk mode but no file
        if ($uploadMode === 'bulk') {
            Yii::$app->session->setFlash('error', 'Bulk upload mode selected but no CSV file uploaded.');
            return $this->render('create', ['model' => $model]);
        }

        // Single upload - normal validation
        if ($model->load($postData)) {
            if ($model->status == 1) {
                $model->publish_at = new \yii\db\Expression('NOW()');
            }

            if ($model->save()) {
                return $this->redirect(['view', 'id' => $model->id]);
            }
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * Process bulk upload from create form
     * @param CollegeNotificationUpdate $model
     * @param array $csvFile
     * @return mixed
     */
    private function processBulkUploadFromForm($model, $csvFile)
    {
        $filePath = $csvFile['tmp_name'];

        if (!file_exists($filePath)) {
            Yii::$app->session->setFlash('error', 'CSV file not found.');
            return $this->redirect('create');
        }

        // MIME Check
        $csvMimes = ['text/x-comma-separated-values', 'text/comma-separated-values', 'application/octet-stream', 'application/vnd.ms-excel', 'application/x-csv', 'text/x-csv', 'text/csv', 'application/csv', 'application/excel', 'application/vnd.msexcel', 'text/plain'];

        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mime = finfo_file($finfo, $filePath);
        finfo_close($finfo);

        if (in_array($mime, $csvMimes) === false) {
            Yii::$app->session->setFlash('error', 'Invalid CSV File - Mime Type Mismatch');
            return $this->redirect('create');
        }

        $csvFileHandle = fopen($filePath, 'r');
        $row = 1;
        $successCount = 0;
        $errorCount = 0;

        while (($data = fgetcsv($csvFileHandle, 1000, ',')) !== false) {
            if ($row > 1) { // Skip header row
                $newModel = new CollegeNotificationUpdate();

                // Set data from CSV (college_id, sub_page)
                if (isset($data[0]) && !empty($data[0])) {
                    $newModel->college_id = (int)$data[0];
                }

                if (isset($data[1]) && !empty($data[1])) {
                    $newModel->sub_page = $data[1];
                }

                // Set data from form (text, content, dates, status)
                $newModel->text = $model->text ?? '';
                $newModel->content = $model->content ?? '';
                $newModel->status = $model->status ?? CollegeNotificationUpdate::STATUS_INACTIVE;
                $newModel->start_date = $model->start_date ?? null;
                $newModel->end_date = $model->end_date ?? null;

                if ($newModel->status == CollegeNotificationUpdate::STATUS_ACTIVE) {
                    $newModel->publish_at = new \yii\db\Expression('NOW()');
                }

                if ($newModel->save()) {
                    $successCount++;
                } else {
                    $errorCount++;
                    $errors = implode(', ', array_map(function($error) {
                        return is_array($error) ? implode(', ', $error) : $error;
                    }, $newModel->errors));
                    Yii::error("Bulk CSV Import failed at line {$row}: {$errors}");
                }
            }
            $row++;
        }

        fclose($csvFileHandle);

        if ($errorCount > 0) {
            Yii::$app->session->setFlash('warning', "Bulk upload completed with {$successCount} successful records and {$errorCount} failed records.");
        } else {
            Yii::$app->session->setFlash('success', "Bulk upload was successful. {$successCount} records created.");
        }

        return $this->redirect('index');
    }

    /**
     * Bulk upload CollegeNotificationUpdate models via CSV.
     * @return mixed
     */
    public function actionBulkUpload()
    {
        $model = new CollegeNotificationUpdate();
        $filePath = $_FILES['csv_upload']['tmp_name'] ?? null;

        if (!empty($filePath) && file_exists($filePath)) {
            if (!$this->isValidCsvMime($filePath)) {
                Yii::$app->session->setFlash('error', 'Invalid CSV File - Mime Type Mismatch');
                return $this->redirect('bulk-upload');
            }

            $csvFile = fopen($filePath, 'r');
            $timestamp = new \yii\db\Expression('NOW()');
            $successCount = 0;
            $errorCount = 0;
            $failDetails = [];
            $row = 1;

            while (($data = fgetcsv($csvFile, 1000, ',')) !== false) {
                $row++;

                if ($row === 2) {
                    continue; // skip header
                }

                $collegeId = (int) ($data[0] ?? 0);
                $slugFromCSV = trim($data[2] ?? '');

                $collegeModel = \common\models\College::findOne($collegeId);
                if (!$collegeModel) {
                    $failDetails[] = "Row {$row}: College with ID {$collegeId} not found.";
                    $errorCount++;
                    continue;
                }

                $validSubPages = array_map('strtolower', array_column($collegeModel->collegeContents, 'sub_page'));
                if (!in_array(strtolower($slugFromCSV), $validSubPages)) {
                    $failDetails[] = "Row {$row}: Sub Page '{$slugFromCSV}' not found for College {$collegeId} || {$collegeModel->name}.";
                    $errorCount++;
                    continue;
                }

                $status = $data[4] ?? '';
                if ($status !== '' && !$this->isValidStatus($status)) {
                    $failDetails[] = "Row {$row}: Invalid status '{$status}'. Must be 0 or 1.";
                    $errorCount++;
                    continue;
                }

                // Date fields
                $dateFormat = 'Y-m-d H:i:s';
                $dateFields = [
                    'created_at' => $data[5] ?? '',
                    'updated_at' => $data[6] ?? '',
                    'start_date' => $data[7] ?? '',
                    'end_date' => $data[8] ?? '',
                    'publish_at' => $data[9] ?? '',
                ];

                $dateErrors = false;
                foreach ($dateFields as $key => $value) {
                    if (!empty($value) && !$this->isValidDate($value, $dateFormat)) {
                        $failDetails[] = "Row {$row}: Invalid {$key} format '{$value}'. Expected format: {$dateFormat}.";
                        $errorCount++;
                        $dateErrors = true;
                        break;
                    }
                }

                if ($dateErrors) {
                    continue;
                }

                // Save model
                $newModel = new CollegeNotificationUpdate();
                $newModel->detachBehavior('timestamp');

                $newModel->college_id = $collegeId;
                $newModel->sub_page = $slugFromCSV;
                $newModel->text = $data[1] ?? null;
                $newModel->content = !empty($data[3]) ? ContentHelper::removeStyleTag(stripslashes(html_entity_decode($data[3]))) : null;
                $newModel->status = ($status !== '') ? (int)$status : CollegeNotificationUpdate::STATUS_INACTIVE;

                foreach ($dateFields as $key => $value) {
                    $newModel->$key = !empty($value) ? $value : (in_array($key, ['created_at', 'updated_at', 'publish_at']) ? $timestamp : null);
                }

                if ($newModel->save()) {
                    $successCount++;
                } else {
                    $errorCount++;
                    $errors = array_map(
                        function ($attr, $messages) {
                            $label = ucfirst(str_replace('_', ' ', $attr));
                            return $label . ': ' . implode(', ', $messages);
                        },
                        array_keys($newModel->getErrors()),
                        $newModel->getErrors()
                    );
                    $failDetails[] = "Row {$row}: " . implode(', ', $errors);
                }
            }

            fclose($csvFile);

            // Final flash
            if ($errorCount > 0) {
                $list = '<ul><li>' . implode('</li><li>', $failDetails) . '</li></ul>';
                Yii::$app->session->setFlash('warning', "CSV Import was successful with {$successCount} records imported.<br>Failed Records: {$errorCount}<br>{$list}");
            } else {
                Yii::$app->session->setFlash('success', "CSV Import was successful. {$successCount} records imported.");
                return $this->redirect(['index']);
            }
        }

        return $this->render('bulk-upload', [
            'model' => $model,
            'errorFlash' => $failDetails ?? [],
        ]);
    }

    private function isValidCsvMime($filePath): bool
    {
        $allowedMimes = [
            'text/x-comma-separated-values',
            'text/comma-separated-values',
            'application/octet-stream',
            'application/vnd.ms-excel',
            'application/x-csv',
            'text/x-csv',
            'text/csv',
            'application/csv',
            'application/excel',
            'application/vnd.msexcel',
            'text/plain',
            'text/html',
        ];
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mime = finfo_file($finfo, $filePath);
        finfo_close($finfo);
        return in_array($mime, $allowedMimes, true);
    }

    private function isValidStatus($status): bool
    {
        return in_array((int)$status, [CollegeNotificationUpdate::STATUS_ACTIVE, CollegeNotificationUpdate::STATUS_INACTIVE], true);
    }

    private function isValidDate($date, $format = 'Y-m-d H:i:s'): bool
    {
        $d = \DateTime::createFromFormat($format, $date);
        return $d && $d->format($format) === $date;
    }

    public function actionGenerateTemplate()
    {
        Yii::$app->controller->layout = false;

        $filename = 'college-notification-template.csv';

        $headers = [
            'college_id',
            'text',
            'sub_page',
            'content',
            'status',
            'created_at',
            'updated_at',
            'start_date',
            'end_date',
            'publish_at'
        ];

        $rows = [
            [
                '520',
                'JoSAA 2025 Round 2 Seat Allotment Result Released',
                'info',
                'Example Text with <a href="https://www.getmyuni.com">link</a> and <b>bold</b> text',
                '1',
                date('Y-m-d H:i:s'),
                date('Y-m-d H:i:s'),
                date('Y-m-d H:i:s'),
                date('Y-m-d H:i:s', strtotime('+10 days')),
                date('Y-m-d H:i:s')
            ]
        ];

        // Set headers
        Yii::$app->response->format = \yii\web\Response::FORMAT_RAW;
        Yii::$app->response->headers->set('Content-Type', 'text/csv; charset=utf-8');
        Yii::$app->response->headers->set('Content-Disposition', 'attachment; filename="' . $filename . '"');

        $fp = fopen('php://output', 'w');
        fputcsv($fp, $headers);
        foreach ($rows as $row) {
            fputcsv($fp, $row);
        }
        fclose($fp);
        Yii::$app->end();
    }


    /**
     * Updates an existing CollegeNotificationUpdate model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing CollegeNotificationUpdate model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the CollegeNotificationUpdate model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return CollegeNotificationUpdate the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = CollegeNotificationUpdate::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
