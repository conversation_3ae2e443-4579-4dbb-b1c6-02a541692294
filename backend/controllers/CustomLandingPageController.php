<?php

namespace backend\controllers;

use Yii;
use common\models\CustomLandingPage;
use backend\models\CustomLandingPageSearch;
use common\models\ClpCollegeAboutUs;
use common\models\ClpCollegeImagePanel;
use common\models\CustomLandingPageLead;
use yii\data\ActiveDataProvider;
use yii\web\Controller;
use yii\web\NotFoundHttpException;

/**
 * CustomLandingPageController implements the CRUD actions for CustomLandingPage model.
 */
class CustomLandingPageController extends Controller
{
    /**
     * Lists all CustomLandingPage models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new CustomLandingPageSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single CustomLandingPage model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        $model = $this->findModel($id);

        $collegeImagePanel = [];
        $collegeAboutUs = [];

        $templates = [
            CustomLandingPage::TEMPLATE_BRAND_SPECIFIC_LANDING_PAGE_VARIATION_2,
            CustomLandingPage::TEMPLATE_BRAND_SPECIFIC_LANDING_PAGE_VARIATION_3,
            CustomLandingPage::TEMPLATE_BRAND_SPECIFIC_LANDING_PAGE
        ];

        if (in_array($model->template_id, $templates) && !empty($model->college_id)) {
            $collegeImagePanel = ClpCollegeImagePanel::findOne(['college_id' => $model->college_id]);
            $collegeAboutUs = ClpCollegeAboutUs::findOne(['college_id' => $model->college_id]);
        }

        if (empty($collegeImagePanel)) {
            $collegeImagePanel = new ClpCollegeImagePanel();
        }

        if (empty($collegeAboutUs)) {
            $collegeAboutUs = new ClpCollegeAboutUs();
        }

        return $this->render('view', [
            'model' => $model,
            'collegeImagePanel' => $collegeImagePanel,
            'collegeAboutUs' => $collegeAboutUs,
        ]);
    }

    /**
     * Creates a new CustomLandingPage model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new CustomLandingPage();

        if ($model->load(Yii::$app->request->post())) {
            $clp = Yii::$app->request->post()['CustomLandingPage'];
            $filteredContent = [];
            $hasError = false;

            if (!empty($clp['featured_content']) && is_array($clp['featured_content'])) {
                foreach ($clp['featured_content'] as $index => $item) {
                    $errors = [];

                    if (empty($item['fc_title'])) {
                        $errors[] = 'Title is required';
                    }

                    if (empty($item['fc_subtitle']) && empty($item['fc_cta'])) {
                        $errors[] = "Either Subtitle or CTA Text for '" . $item['fc_title'] . "' must be provided";
                    }

                    if (empty($errors)) {
                        $filteredContent[] = [
                            'fc_title' => $item['fc_title'],
                            'fc_subtitle' => $item['fc_subtitle'] ?? '',
                            'fc_cta' => $item['fc_cta'] ?? '',
                        ];
                    }
                }
            }

            $model->featured_content = !empty($filteredContent) ? $filteredContent : null;

            $model->city_id = !empty($clp['city_id']) ?  $clp['city_id'] : null;
            $model->state_id = !empty($clp['state_id']) ?  $clp['state_id'] : null;
            $model->college_id = $clp['college_id'] ?? null;
            $model->stream_id = !empty($clp['stream_id']) ?  $clp['stream_id'] : null;
            $model->level_id = !empty($clp['level_id']) ?  $clp['level_id'] : null;
            $model->course_id = !empty($clp['course_id']) ?  $clp['course_id'] : null;
            $model->program_id = !empty($clp['program_id']) ?  $clp['program_id'] : null;
            $model->stream_widget = !empty($clp['stream_widget']) ?  $clp['stream_widget'] : null;
            $model->college_usp_widget = !empty($clp['college_usp_widget']) ?  $clp['college_usp_widget'] : null;
            $model->campus_widget = !empty($clp['campus_widget']) ?  $clp['campus_widget'] : null;
            $model->partner_college_widget = !empty($clp['partner_college_widget']) ?  $clp['partner_college_widget'] : null;
            $model->program_widget = !empty($clp['program_widget']) ?  $clp['program_widget'] : null;
            $model->recruitment_widget = !empty($clp['recruitment_widget']) ?  $clp['recruitment_widget'] : null;

            if (!$hasError && $model->validate() && $model->save(false)) {
                Yii::$app->session->setFlash('success', 'Updated successfully.');
                return $this->redirect(['view', 'id' => $model->id]);
            } else {
                Yii::$app->session->setFlash('error', 'Validation failed. Please check your input.');
            }
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * Updates an existing CustomLandingPage model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post())) {
            $clp = Yii::$app->request->post()['CustomLandingPage'];
            $filteredContent = [];
            $hasError = false;

            if (!empty($clp['featured_content']) && is_array($clp['featured_content'])) {
                foreach ($clp['featured_content'] as $index => $item) {
                    $errors = [];

                    if (empty($item['fc_title'])) {
                        $errors[] = 'Title is required';
                    }

                    if (empty($item['fc_subtitle']) && empty($item['fc_cta'])) {
                        $errors[] = "Either Subtitle or CTA Text for '" . $item['fc_title'] . "' must be provided";
                    }

                    if (empty($errors)) {
                        $filteredContent[] = [
                            'fc_title' => $item['fc_title'],
                            'fc_subtitle' => $item['fc_subtitle'] ?? '',
                            'fc_cta' => $item['fc_cta'] ?? '',
                        ];
                    }
                }
            }

            $model->featured_content = !empty($filteredContent) ? $filteredContent : null;

            $model->city_id = !empty($clp['city_id']) ?  $clp['city_id'] : null;
            $model->state_id = !empty($clp['state_id']) ?  $clp['state_id'] : null;
            $model->college_id = !empty($clp['college_id']) ? $clp['college_id'] : null;
            $model->stream_id = !empty($clp['stream_id']) ?  $clp['stream_id'] : null;
            $model->level_id = !empty($clp['level_id']) ?  $clp['level_id'] : null;
            $model->course_id = !empty($clp['course_id']) ?  $clp['course_id'] : null;
            $model->program_id = !empty($clp['program_id']) ?  $clp['program_id'] : null;
            $model->stream_widget = !empty($clp['stream_widget']) ?  $clp['stream_widget'] : null;
            $model->college_usp_widget = !empty($clp['college_usp_widget']) ?  $clp['college_usp_widget'] : null;
            $model->campus_widget = !empty($clp['campus_widget']) ?  $clp['campus_widget'] : null;
            $model->partner_college_widget = !empty($clp['partner_college_widget']) ?  $clp['partner_college_widget'] : null;
            $model->program_widget = !empty($clp['program_widget']) ?  $clp['program_widget'] : null;
            $model->recruitment_widget = !empty($clp['recruitment_widget']) ?  $clp['recruitment_widget'] : null;

            if (!$hasError && $model->validate() && $model->save(false)) {
                Yii::$app->session->setFlash('success', 'Updated successfully.');
                return $this->redirect(['view', 'id' => $model->id]);
            } else {
                Yii::$app->session->setFlash('error', 'Validation failed. Please check your input.');
            }
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing CustomLandingPage model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the CustomLandingPage model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return CustomLandingPage the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = CustomLandingPage::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    public function actionScriptInjection($id)
    {
        $scriptKeys = [
            'remarketing',
            'conversion',
            'analytics',
        ];

        $model = $this->findModel($id);

        $scriptData = $model->script_injection ?? [];
        if ($model->load(Yii::$app->request->post())) {
            $clp = Yii::$app->request->post();

            $model->script_injection = !empty($clp['script_injection']) ? $clp['script_injection'] : null;

            if ($model->save()) {
                Yii::$app->session->setFlash('success', 'Updated successfully.');
                return $this->redirect(['view', 'id' => $model->id]);
            } else {
                Yii::$app->session->setFlash('error', 'Validation failed. Please check your input.');
            }
        }

        return $this->render('script-injection', [
            'model' => $model,
            'scriptKeys' => $scriptKeys,
            'scriptData' => $scriptData,
        ]);
    }

    public function actionLeadCountIndex($slug)
    {
        $searchModel = new CustomLandingPageSearch();
        $dataProvider = $searchModel->leadCountSearch(Yii::$app->request->queryParams, $slug);

        $totalCount = CustomLandingPageLead::find()->where(['slug' => $slug])->count();
        $distinctCount = CustomLandingPageLead::find()->where(['slug' => $slug])->groupBy(['phone'])->count();

        return $this->render('lead_count', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'clpSlug' => $slug,
            'totalCount' => $totalCount,
            'distinctCount' => $distinctCount,
        ]);
    }

    public function actionExportRange($slug, $from = null, $to = null)
    {
        $query = CustomLandingPageLead::find()
            ->where(['slug' => $slug])
            ->with(['college', 'campus', 'stream', 'degree', 'course', 'program']);

        if (!empty($from)) {
            $query->andWhere(['>=', 'created_at', $from . ' 00:00:00']);
        }

        if (!empty($to)) {
            $query->andWhere(['<=', 'created_at', $to . ' 23:59:59']);
        }

        $leads = $query->orderBy(['created_at' => SORT_DESC])->all();

        $filename = "clp_leads_{$slug}_{$from}_to_{$to}.csv";

        $this->exportData($leads, $filename);
    }

    public function exportData($leads, $filename)
    {
        header('Content-Type: text/csv');
        header("Content-Disposition: attachment; filename=$filename");

        $fp = fopen('php://output', 'w');
        fputcsv($fp, [
            'ID',
            'Name',
            'Email',
            'Phone',
            'College',
            'Campus',
            'Stream',
            'Degree',
            'Course',
            'Program',
            'UTM Source',
            'UTM Medium',
            'UTM Campaign',
            'Platform',
            'Created At'
        ]);

        foreach ($leads as $lead) {
            fputcsv($fp, [
                $lead->id,
                $lead->name,
                $lead->email,
                $lead->phone,
                $lead->college->name ?? '',
                $lead->campus->name ?? '',
                $lead->stream->name ?? '',
                $lead->degree->name ?? '',
                $lead->course->name ?? '',
                $lead->program->name ?? '',
                $lead->utm_source,
                $lead->utm_medium,
                $lead->utm_campaign,
                $lead->platform,
                $lead->created_at
            ]);
        }

        fclose($fp);
        Yii::$app->end();
    }

    public function actionExportDay($slug, $date = null)
    {
        $query = CustomLandingPageLead::find()
            ->where(['slug' => $slug]);
        if (!empty($date)) {
            $query->andWhere(['DATE(created_at)' => $date]);
        }
        $query->with(['college', 'campus', 'stream', 'degree', 'course', 'program']);
        $leads = $query->all();

        $date = !empty($date) ? $date : 'all';
        $filename = "clp_leads_{$slug}_{$date}.csv";

        $this->exportData($leads, $filename);
    }

    public function actionClone($slug)
    {
        $original = CustomLandingPage::find()->where(['slug' => $slug])->one();

        if (!$original) {
            throw new NotFoundHttpException("Page not found for slug: $slug");
        }

        $clone = new CustomLandingPage();

        // Copy all attributes
        $clone->setAttributes($original->getAttributes());

        $clone->id = null;
        $clone->slug = null; // must be manually filled by user in create form
        $clone->isNewRecord = true;

        return $this->render('create', [
            'model' => $clone,
        ]);
    }
}
