<?php

use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\ArrayHelper;
use common\helpers\DataHelper;
use common\models\CollegeNotificationUpdate;

/* @var $this yii\web\View */
/* @var $searchModel common\models\CollegeNotificationUpdateSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'College Notification Updates';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="college-notification-update-index box box-primary">
       <div class="box-header with-border">

       <div class="box-header with-border">
    <p>
        <?= Html::a('Bulk Upload', ['bulk-upload'], ['class' => 'btn btn-info','style' => 'float: right; margin-right: 10px;']) ?>
        <?= Html::a('Create College Notification Update', ['create'], ['class' => 'btn btn-success','style' => 'float: right; margin-right: 10px;']) ?>
    </p>
       </div>

    <?php // echo $this->render('_search', ['model' => $searchModel]); ?>

    <?= GridView::widget([
        'dataProvider' => $dataProvider,
        'filterModel' => $searchModel,
        'columns' => [
            ['class' => 'yii\grid\SerialColumn'],

            'sub_page',

            [
                'attribute' => 'college_id',
                'label' => 'College Name',
                'value' => function ($m) {
                    return $m->college->name ?? '';
                }
            ],
            'text',
            [
                'attribute' => 'status',
                'value' => function ($model) {
                    return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', CollegeNotificationUpdate::class), $model->status);
                },
                'filter' => DataHelper::getConstantList('STATUS', CollegeNotificationUpdate::class)
            ],
            ['class' => 'yii\grid\ActionColumn'],
        ],
    ]); ?>

       </div>
</div>
