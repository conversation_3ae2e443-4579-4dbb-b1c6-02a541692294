<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use yii\web\JsExpression;
use yii\helpers\Url;
use kartik\depdrop\DepDrop;
use common\models\College;
use common\helpers\CollegeHelper;
use common\helpers\DataHelper;
use common\models\CollegeNotificationUpdate;
use kartik\date\DatePicker;
use kartik\datetime\DateTimePicker;

/* @var $this yii\web\View */
/* @var $model common\models\CollegeNotificationUpdate */
/* @var $form yii\widgets\ActiveForm */

$data =  ArrayHelper::map(College::find()->andWhere(['id' => $model->college_id])->all(), 'id', 'name');

?>

<div class="college-notification-update-form  box box-primary">
    <div class="box-body">

        <!-- Upload Mode Toggle -->
        <div class="form-group">
            <label class="control-label">Upload Mode</label>
            <div class="btn-group btn-group-toggle" data-toggle="buttons">
                <label class="btn btn-outline-primary active" id="single-mode-btn">
                    <input type="radio" name="upload_mode" id="single_mode" value="single" checked> Single Upload
                </label>
                <label class="btn btn-outline-primary" id="bulk-mode-btn">
                    <input type="radio" name="upload_mode" id="bulk_mode" value="bulk"> Bulk Upload
                </label>
            </div>
        </div>

        <?php $form = ActiveForm::begin(['options' => ['enctype' => 'multipart/form-data']]); ?>

        <!-- Hidden field to track upload mode -->
        <?= Html::hiddenInput('upload_mode', 'single', ['id' => 'upload_mode_field']) ?>
        <!-- Single Upload Fields -->
        <div id="single-upload-fields">
            <?=
            $form->field($model, 'college_id')->widget(Select2::class, [
                'disabled' => !$model->isNewRecord,
                'data' => $data, // array of text to show in the tag for the selected items
                'options' => [
                    'placeholder' => '--Select--',
                    'multiple' => false,
                ],
                'pluginOptions' => [
                    'allowClear' => true,
                    'minimumInputLength' => 3,
                    'language' => [
                        'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                    ],
                    'ajax' => [
                        'url' => ['../ajax/college-list'],
                        'dataType' => 'json',
                        'data' => new JsExpression('function(params) {return {q:params.term}; }')
                    ],
                    'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                    'templateResult' => new JsExpression('function(data) { return data.text; }'),
                    'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                ],
                'pluginEvents' => [
                    'change' => 'function(data) {
                    $.post( "' . Url::toRoute('college-content/check-college-page-combination') . '", { college: $(this).val(),page:$("#sub_page").find(":selected").val()}).done(function( data ) { if(data){
                        $(".sub-page-drpdown").show();
                    }else{
                        $(".sub-page-drpdown").hide();
                        $("#parent_id").val("").trigger("change");
                    }});
    }',
                ]
            ])->label('College Name');
            ?>

            <?= $form->field($model, 'sub_page')->widget(DepDrop::class, [
                'type' => DepDrop::TYPE_SELECT2,
                'options' => [
                    'id' => 'sub_page',
                    'placeholder' => '--Select Sub Page--',
                    'multiple' => false,
                ],
                // 'data' => !empty($model->sub_page) ? ArrayHelper::map($model->sub_page, 'id', 'name')  : [],
                'select2Options' => ['pluginOptions' => [
                    'allowClear' => true,
                    'disabled' => !$model->isNewRecord,
                ]],
                'pluginOptions' => [
                    'depends' => ['collegenotificationupdate-college_id'],
                    'placeholder' => 'Select Sub Page...',
                    'url' => Url::to(['/college-content/all-active-subpage'])
                ],

            ])->label('Sub Page');
            ?>
        </div>

        <!-- Bulk Upload Fields -->
        <div id="bulk-upload-fields" style="display: none;">
            <div class="form-group">
                <label class="control-label">CSV File (College ID & Sub Page)</label>
                <div class="dropzone-wrapper">
                    <div class="dropzone-desc">
                        <i class="glyphicon glyphicon-download-alt"></i>
                        <p>Choose a CSV file or drag it here.</p>
                        <p><small>CSV should contain: college_id, sub_page</small></p>
                    </div>
                    <input type="file" name="bulk_csv" class="dropzone" accept=".csv" />
                </div>
            </div>

            <div class="alert alert-info">
                <strong>CSV Format:</strong> The CSV file should contain two columns: <code>college_id</code> and <code>sub_page</code>
                <br><strong>Example:</strong>
                <pre>college_id,sub_page
32222,info
32222,admission
12885,cut-off</pre>
                <a href="/sample-bulk-college-subpage.csv" class="btn btn-sm btn-info" style="margin-top: 10px;">
                    <i class="glyphicon glyphicon-download"></i> Download Sample CSV
                </a>
            </div>
        </div>
    </div>

    <!-- Common Fields (always visible) -->
    <?=
    $this->render('/widget/tinymce', [
        'form' => $form,
        'model' => $model,
        'type' => College::ENTITY_COLLEGE,
        'entity' => 'content'
    ])
    ?>
    <?= $form->field($model, 'text')->textInput()->label('Heading Text') ?>

    <?=
    $form->field($model, 'start_date')->widget(DatePicker::class, [
        'value' => '',
        'options' => ['placeholder' => 'Select date ...', 'class' => 'start_date'],
        'type' => DatePicker::TYPE_COMPONENT_APPEND,
        'pluginOptions' => [
            'format' => 'yyyy-mm-dd',
            'minViewMode' => 'month',
            'startDate' => date('Y-m-d'),
            'todayHighlight' => true,
        ],
    ])->label('Start Date');
    ?>
    <?=
    $form->field($model, 'end_date')->widget(DatePicker::class, [
        'value' => '',
        'options' => ['placeholder' => 'Select date ...', 'class' => 'start_date'],
        'type' => DatePicker::TYPE_COMPONENT_APPEND,
        'pluginOptions' => [
            'format' => 'yyyy-mm-dd',
            'minViewMode' => 'month',
            'startDate' => date('Y-m-d'),
            'todayHighlight' => true,
        ],
    ])->label('End Date');
    ?>

    <?= $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', CollegeNotificationUpdate::class)) ?>

    <div class="form-group">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>

<style>
    .dropzone-wrapper {
        border: 2px dashed #91b0b3;
        color: #92b0b3;
        position: relative;
        height: 150px;
        border-radius: 5px;
    }

    .dropzone-desc {
        position: absolute;
        margin: 0 auto;
        left: 0;
        right: 0;
        text-align: center;
        width: 40%;
        top: 50px;
        font-size: 16px;
    }

    .dropzone-desc p {
        margin: 5px 0;
    }

    .dropzone {
        position: absolute;
        top: 0px;
        right: 0;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        cursor: pointer;
    }

    .dropzone-wrapper:hover {
        background: #f8f9fa;
        border-color: #007bff;
    }

    .dropzone-wrapper:hover .dropzone-desc {
        color: #007bff;
    }
</style>

<?php
$script = <<<JS
$(document).ready(function() {
    // Toggle between single and bulk upload modes
    $('input[name="upload_mode"]').change(function() {
        var mode = $(this).val();
        $('#upload_mode_field').val(mode);

        if (mode === 'single') {
            $('#single-upload-fields').show();
            $('#bulk-upload-fields').hide();
            $('#single-mode-btn').addClass('active');
            $('#bulk-mode-btn').removeClass('active');
        } else {
            $('#single-upload-fields').hide();
            $('#bulk-upload-fields').show();
            $('#bulk-mode-btn').addClass('active');
            $('#single-mode-btn').removeClass('active');
        }
    });

    // File drop functionality
    $('.dropzone').on('change', function() {
        var fileName = $(this)[0].files[0] ? $(this)[0].files[0].name : '';
        if (fileName) {
            $(this).siblings('.dropzone-desc').html('<i class="glyphicon glyphicon-ok"></i><p>File selected: ' + fileName + '</p>');
        }
    });

    // Drag and drop events
    $('.dropzone-wrapper').on('dragover', function(e) {
        e.preventDefault();
        e.stopPropagation();
        $(this).addClass('dragover');
    });

    $('.dropzone-wrapper').on('dragleave', function(e) {
        e.preventDefault();
        e.stopPropagation();
        $(this).removeClass('dragover');
    });

    $('.dropzone-wrapper').on('drop', function(e) {
        e.preventDefault();
        e.stopPropagation();
        $(this).removeClass('dragover');

        var files = e.originalEvent.dataTransfer.files;
        if (files.length > 0) {
            $(this).find('.dropzone')[0].files = files;
            $(this).find('.dropzone').trigger('change');
        }
    });
});
JS;
$this->registerJs($script);
