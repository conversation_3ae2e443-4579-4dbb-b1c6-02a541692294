<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use common\models\CollegeNotificationUpdate;

/* @var $this yii\web\View */
/* @var $model common\models\CollegeNotificationUpdate */

$this->title = 'Bulk Upload College Notification Updates';
$this->params['breadcrumbs'][] = ['label' => 'College Notification Updates', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>

<div class="college-notification-update-bulk-upload">
    
    <div class="box box-primary">
        <div class="box-header" style="background: antiquewhite;">
            <div class="col-md-12">
                <h4>CSV Upload Instructions</h4>
                <p>Please ensure your CSV file follows the exact format below:</p>
            </div>
            
            <div class="col-md-12">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Column</th>
                                <th>Field Name</th>
                                <th>Data Type</th>
                                <th>Required</th>
                                <th>Description</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>college_id</td>
                                <td>Integer</td>
                                <td>Yes</td>
                                <td>College ID from college table</td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>text</td>
                                <td>String</td>
                                <td>Yes</td>
                                <td>Heading text for the notification</td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>sub_page</td>
                                <td>String</td>
                                <td>Yes</td>
                                <td>Sub page identifier (info, admission, cut-off, etc.)</td>
                            </tr>
                            <tr>
                                <td>4</td>
                                <td>content</td>
                                <td>Text</td>
                                <td>Yes</td>
                                <td>HTML content of the notification</td>
                            </tr>
                            <tr>
                                <td>5</td>
                                <td>status</td>
                                <td>Integer</td>
                                <td>Yes</td>
                                <td>0 = Inactive, 1 = Active</td>
                            </tr>
                            <tr>
                                <td>6</td>
                                <td>created_at</td>
                                <td>DateTime</td>
                                <td>No</td>
                                <td>Auto-generated (Format: YYYY-MM-DD HH:MM:SS)</td>
                            </tr>
                            <tr>
                                <td>7</td>
                                <td>updated_at</td>
                                <td>DateTime</td>
                                <td>No</td>
                                <td>Auto-generated (Format: YYYY-MM-DD HH:MM:SS)</td>
                            </tr>
                            <tr>
                                <td>8</td>
                                <td>start_date</td>
                                <td>DateTime</td>
                                <td>Yes</td>
                                <td>Format: YYYY-MM-DD HH:MM:SS</td>
                            </tr>
                            <tr>
                                <td>9</td>
                                <td>end_date</td>
                                <td>DateTime</td>
                                <td>No</td>
                                <td>Format: YYYY-MM-DD HH:MM:SS</td>
                            </tr>
                            <tr>
                                <td>10</td>
                                <td>publish_at</td>
                                <td>DateTime</td>
                                <td>No</td>
                                <td>Auto-set if status=1, Format: YYYY-MM-DD HH:MM:SS</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="col-md-12">
                <div class="alert alert-warning">
                    <strong>Sample Data Format:</strong>
                    <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; font-size: 12px;">
college_id	text	sub_page	content	status	created_at	updated_at	start_date	end_date	publish_at
32222	JoSAA 2025 Round 2 Seat Allotment Result Released	info	The JoSAA 2025 Round 2 seat allotment result is now available. Candidates allotted a seat in this round must complete online reporting, including fee payment, document upload, and responding to any queries, by June 25. The final date to pay the seat acceptance fee is June 29, 2025. The Round 3 seat allotment result will be announced on July 2, 2025.&lt;a href='https://www.getmyuni.com/college/iiit-agartala-indian-institute-of-information-technology-cut-off'&gt; Check IIIT Agartala Cutoff 2025&lt;/a&gt;	1	2025-06-26 19:52:19	2025-06-27 11:58:13	2025-06-27 00:00:00	2025-07-26 00:00:00	2025-06-27 00:00:00
32222	JoSAA 2025 Round 2 Seat Allotment Result Released	admission	The JoSAA 2025 Round 2 seat allotment result is now available. Candidates allotted a seat in this round must complete online reporting, including fee payment, document upload, and responding to any queries, by June 25. The final date to pay the seat acceptance fee is June 29, 2025. The Round 3 seat allotment result will be announced on July 2, 2025.&lt;a href='https://www.getmyuni.com/college/iiit-agartala-indian-institute-of-information-technology-cut-off'&gt; Check IIIT Agartala Cutoff 2025&lt;/a&gt;	1	2025-06-26 19:52:19	2025-06-27 11:58:13	2025-06-27 00:00:00	2025-07-26 00:00:00	2025-06-27 00:00:00
                    </pre>
                </div>
            </div>
            
            <div class="col-md-12">
                <div class="alert alert-info">
                    <strong>Status Values:</strong>
                    <ul>
                        <li><?= CollegeNotificationUpdate::STATUS_INACTIVE ?> = Inactive</li>
                        <li><?= CollegeNotificationUpdate::STATUS_ACTIVE ?> = Active</li>
                    </ul>

                    <strong>Common Sub Page Values:</strong>
                    <ul>
                        <li>info, admission, courses-fees, cut-off, reviews, placements, result, facilities, images-videos, scholarships, qna, ranking, alumni, hostel, application-form, syllabus, verdict, news</li>
                    </ul>

                    <strong>Important Notes:</strong>
                    <ul>
                        <li><strong>created_at</strong> and <strong>updated_at</strong> columns in CSV are ignored - they are auto-generated by the system</li>
                        <li>If <strong>publish_at</strong> is empty and status=1, it will be auto-set to current timestamp</li>
                        <li>All HTML content should be properly escaped in the CSV</li>
                    </ul>
                </div>
            </div>
            
            <div class="col-md-12">
                <a href="/sample-college-notification-data.csv" class="btn btn-primary btn-flat" style="float: right;">Download Sample CSV</a>
            </div>
        </div>
    </div>
    
    <br>
    
    <div class="box box-primary">
        <div class="box-header with-border">
            <h3 class="box-title">Upload CSV File</h3>
        </div>
        
        <?php $form = ActiveForm::begin(['options' => ['enctype' => 'multipart/form-data']]); ?>
        <div class="box-body">
            <div class="col-md-12">
                <div class="form-group">
                    <label class="control-label" for="csv_upload">CSV File</label>
                    <?= Html::fileInput('csv_upload', '', [
                        'class' => 'form-control', 
                        'required' => true, 
                        'accept' => '.csv'
                    ]) ?>
                    <p class="help-block">Please select a CSV file with college notification data.</p>
                </div>
            </div>
        </div>
        
        <div class="box-footer">
            <?= Html::submitButton('Upload CSV', ['class' => 'btn btn-success btn-flat']) ?>
            <?= Html::a('Cancel', ['index'], ['class' => 'btn btn-default btn-flat']) ?>
        </div>
        <?php ActiveForm::end(); ?>
    </div>
</div>
