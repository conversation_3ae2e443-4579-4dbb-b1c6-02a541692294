<?php

use common\helpers\DataHelper;
use common\models\City;
use common\models\ClpCollegeUsp;
use common\models\Course;
use common\models\CustomLandingPage;
use common\models\Degree;
use common\models\College;
use common\models\Program;
use common\models\State;
use common\models\Stream;
use common\models\TopRecruiters;
use frontend\helpers\Url;
use kartik\select2\Select2;
use unclead\multipleinput\MultipleInput;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\web\JsExpression;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\CustomLandingPage */
/* @var $form yii\widgets\ActiveForm */

$currentAction = Yii::$app->controller->action->id;

$formAction = null;
if ($currentAction === 'clone') {
    $formAction = Url::to(['create']);
}

$collegeData = [];
if (!empty($model->college_id)) {
    $collegeData[] = ArrayHelper::map(College::find()->where(['id' => $model->college_id])->all(), 'id', 'name');
}

if (!empty($model->featured_content) && is_string($model->featured_content)) {
    $model->featured_content = json_decode($model->featured_content, true);
}

$streamData = [];
if (isset($model->stream_id) && !empty($model->stream_id)) {
    $streamData[] = ArrayHelper::map(Stream::find()->where(['id' => $model->stream_id])->all(), 'id', 'name');
}

$courseData = [];
if (isset($model->course_id) && !empty($model->course_id)) {
    $courseData[] = ArrayHelper::map(Course::find()->where(['id' => $model->course_id])->all(), 'id', 'name');
}

$campData = [];
if (!empty($model->campus)) {
    foreach ($model->campus as $value) {
        $campData[] = ArrayHelper::map(College::find()->where(['id' => $value])->all(), 'id', 'name');
    }
}

$streamWidData = [];
if (isset($model->stream_widget) && !empty($model->stream_widget)) {
    $streamWidData[] = ArrayHelper::map(Stream::find()->where(['id' => $model->stream_widget])->all(), 'id', 'name');
}

if (!empty($model->college_usp_widget) && is_string($model->college_usp_widget)) {
    $model->college_usp_widget = json_decode($model->college_usp_widget, true);
}

if (!empty($model->recruitment_widget) && is_string($model->recruitment_widget)) {
    $model->recruitment_widget = json_decode($model->recruitment_widget, true);
}

$programWidData = [];
if (isset($model->program_widget) && !empty($model->program_widget)) {
    $programWidData[] = ArrayHelper::map(Program::find()->where(['id' => $model->program_widget])->all(), 'id', 'name');
}

$recruiterWidData = [];
if (isset($model->recruitment_widget) && !empty($model->recruitment_widget)) {
    $recruiterWidData[] = ArrayHelper::map(TopRecruiters::find()->where(['id' => $model->recruitment_widget])->all(), 'id', 'recruiter_name');
}

$collegeUspWidData = [];
if (isset($model->college_usp_widget) && !empty($model->college_usp_widget)) {
    $collegeUspWidData[] = ArrayHelper::map(ClpCollegeUsp::find()->where(['id' => $model->college_usp_widget])->all(), 'id', 'usp_title');
}

$campusWidData = [];
if (isset($model->campus_widget) && !empty($model->campus_widget)) {
    $campusWidData[] = ArrayHelper::map(College::find()->where(['id' => $model->campus_widget])->all(), 'id', 'name');
}

$partnerCollegeWidData = [];
if (isset($model->partner_college_widget) && !empty($model->partner_college_widget)) {
    $partnerCollegeWidData[] = ArrayHelper::map(College::find()->where(['id' => $model->partner_college_widget])->all(), 'id', 'name');
}

?>

<div class="custom-landing-page-form box box-primary">
    <?php $form = ActiveForm::begin(['action' => $formAction,  'method' => 'post',]); ?>
    <div class="box-body table-responsive">

        <div class="row">
            <div class="col-md-4">
                <?= $form->field($model, 'template_id')
                    ->dropDownList(
                        DataHelper::getConstantList('TEMPLATE', CustomLandingPage::class),
                        ['prompt' => 'Select Template']
                    ); ?>
            </div>
            <?php
            if (!$model->isNewRecord) {
                $selectedValueCollege = [];
                if (!empty($collegeData)) {
                    foreach ($collegeData as $value) {
                        if (!empty($value)) {
                            $selectedValueCollege[array_keys($value)[0]] = ['selected' => true];
                        }
                    }
                }
            }
            ?>


            <div class="col-md-4">
                <?= $form->field($model, 'college_id')->widget(Select2::classname(), [
                    'data' => $collegeData ?? [], //array of text to show in the field for the selected items
                    'options' => [
                        'placeholder' => '--Select--',
                        'multiple' => false,
                        'options' => $selectedValueCollege ?? [],
                    ],
                    'disabled' => (in_array($model->template_id, [1, 2, 3, 7])) || ($model->isNewRecord && $currentAction !== 'clone'),
                    'pluginOptions' => [
                        'allowClear' => true,
                        'minimumInputLength' => 3,
                        'language' => [
                            'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                        ],
                        'ajax' => [
                            'url' => ['../ajax/college-list'],
                            'dataType' => 'json',
                            'data' => new JsExpression('function(params) {return {q:params.term}; }')
                        ],
                        'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                        'templateResult' => new JsExpression('function(data) { return data.text; }'),
                        'templateSelection' => new JsExpression('function (data) { return data.name || $("#customlandingpage-college_id option[value=\'"+data.id+"\']").text(); }'),
                    ],
                ])->label('College Name'); ?>
            </div>

            <div class="col-md-4">
                <?= $form->field($model, 'slug')->textInput(['maxlength' => true]) ?>
            </div>

        </div>

        <div class="row">
            <div class="col-md-4">
                <?= $form->field($model, 'redirection_url')->textInput(['maxlength' => true]) ?>
            </div>

            <div class="col-md-4">
                <?= $form->field($model, 'page_title')->textInput(['maxlength' => true]) ?>
            </div>
            <div class="col-md-4">
                <?= $form->field($model, 'page_sub_title')->textInput(['maxlength' => true]) ?>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <?=
                $this->render('/widget/tinymce', [
                    'form' => $form,
                    'model' => $model,
                    'type' => '',
                    'entity' => 'page_description',
                ])
                ?>
            </div>
        </div>

        <div class="text-muted well well-sm no-shadow">
            <div class="row">
                <div class="col-md-12">
                    <div style="max-height: 300px; overflow-y: auto; overflow-x: hidden; border: 1px solid #ccc; padding: 10px;">
                        <div class="row">
                            <div class="col-md-12">
                                <?= $form->field($model, 'featured_content')->widget(MultipleInput::class, [
                                    'id' => 'custom-multiple-input-id-featured_content',
                                    'max' => 30,
                                    'allowEmptyList' => false,
                                    'enableGuessTitle' => true,
                                    'addButtonPosition' => MultipleInput::POS_HEADER,
                                    'columns' => [
                                        [
                                            'name' => 'fc_title',
                                            'title' => 'Title',
                                            'type' => Select2::class,
                                            'options' => [
                                                'data' => DataHelper::$clpFeatureContents ?? [],
                                                'options' => [
                                                    'placeholder' => '--Select--',
                                                    'multiple' => false,
                                                    'class' => 'fc-title-select',
                                                ]
                                            ],
                                            'enableError' => true,
                                        ],
                                        ['name' => 'fc_subtitle', 'title' => 'Subtitle', 'type' => 'textInput', 'enableError' => true,],
                                        ['name' => 'fc_cta', 'title' => 'CTA Text', 'type' => 'textInput', 'enableError' => true,],
                                    ],
                                ]); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-muted well well-sm no-shadow">
            <label> Featured Content Data</label>
            <div class="row">
                <div class="col-md-12">
                    <div style="max-height: 300px; overflow-y: auto; overflow-x: hidden; border: 1px solid #ccc; padding: 10px;">
                        <?php
                        if (!$model->isNewRecord) {
                            $selectedValueStreamWid = [];
                            if (!empty($streamWidData)) {
                                foreach ($streamWidData as $value) {
                                    if (!empty($value)) {
                                        $selectedValueStreamWid[array_keys($value)[0]] = ['selected' => true];
                                    }
                                }
                            }
                        }
                        ?>
                        <div class="row">
                            <div class="col-md-6">
                                <?= $form->field($model, 'stream_widget')->widget(Select2::classname(), [
                                    'data' => $streamWidData ?? [],
                                    'options' => [
                                        'placeholder' => '--Select Stream--',
                                        'multiple' => true,
                                        'options' => $selectedValueStreamWid ?? [],
                                    ],
                                    'pluginOptions' => [
                                        'allowClear' => true,
                                        'maximumInputLength' => 1,
                                        'language' => [
                                            'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                                        ],
                                        'ajax' => [
                                            'url' => ['../ajax/stream-list'],
                                            'dataType' => 'json',
                                            'data' => new JsExpression('function(params) {return {query:params.term}; }')
                                        ],
                                        'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                                        'templateResult' => new JsExpression('function(data) { return data.text; }'),
                                        'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                                    ],
                                ])->label('Stream Widget');
?>
                            </div>

                            <div class="col-md-6">
                                <?php
                                if (!$model->isNewRecord) {
                                    $selectedValueCollegeUsp = [];
                                    if (!empty($collegeUspWidData)) {
                                        foreach ($collegeUspWidData as $value) {
                                            if (!empty($value)) {
                                                $selectedValueCollegeUsp[array_keys($value)[0]] = ['selected' => true];
                                            }
                                        }
                                    }
                                }
                                ?>
                                <?= $form->field($model, 'college_usp_widget')->widget(Select2::classname(), [
                                    'data' => $collegeUspWidData ?? [],
                                    'options' => [
                                        'placeholder' => '--Select College USPs--',
                                        'multiple' => true,
                                        'options' => $selectedValueCollegeUsp ?? [],
                                    ],
                                    'pluginOptions' => [
                                        'allowClear' => true,
                                        'minimumInputLength' => 0,
                                        'language' => [
                                            'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                                        ],
                                        'ajax' => [
                                            'url' => ['../ajax/usp-list'],
                                            'dataType' => 'json',
                                            'data' => new JsExpression('function(params) {return {query:params.term, college_id:$("#customlandingpage-college_id").val()}; }')
                                        ],
                                        'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                                        'templateResult' => new JsExpression('function(data) {return data.text; }'),
                                        'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                                    ],
                                ])->label('College USPs Widget'); ?>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <?php
                                if (!$model->isNewRecord) {
                                    $selectedValueRecruiter = [];
                                    if (!empty($recruiterWidData)) {
                                        foreach ($recruiterWidData as $value) {
                                            if (!empty($value)) {
                                                $selectedValueRecruiter[array_keys($value)[0]] = ['selected' => true];
                                            }
                                        }
                                    }
                                }
                                ?>
                                <?= $form->field($model, 'recruitment_widget')->widget(Select2::classname(), [
                                    'data' => $recruiterWidData ?? [],
                                    'options' => [
                                        'placeholder' => '--Select Recruiters--',
                                        'multiple' => true,
                                        'options' => $selectedValueRecruiter ?? [],
                                    ],
                                    'pluginOptions' => [
                                        'allowClear' => true,
                                        'minimumInputLength' => 1,
                                        'language' => [
                                            'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                                        ],
                                        'ajax' => [
                                            'url' => ['../ajax/recruiter-list'],
                                            'dataType' => 'json',
                                            'data' => new JsExpression('function(params) {return {query:params.term}; }')
                                        ],
                                        'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                                        'templateResult' => new JsExpression('function(data) {return data.text; }'),
                                        'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                                    ],
                                ])->label('Recruiters Widget'); ?>
                            </div>
                            <?php
                            if (!$model->isNewRecord) {
                                $selectedValueProgramWid = [];
                                if (!empty($programWidData)) {
                                    foreach ($programWidData as $value) {
                                        if (!empty($value)) {
                                            $selectedValueProgramWid[array_keys($value)[0]] = ['selected' => true];
                                        }
                                    }
                                }
                            }
                            ?>
                            <div class="col-md-6">
                                <?= $form->field($model, 'program_widget')->widget(Select2::classname(), [
                                    'data' => $programWidData ?? [],
                                    'options' => [
                                        'placeholder' => '--Select Programs--',
                                        'multiple' => true,
                                        'options' => $selectedValueProgramWid ?? [],
                                    ],
                                    // 'disabled' => !$model->isNewRecord,
                                    'pluginOptions' => [
                                        'allowClear' => true,
                                        'minimumInputLength' => 1,
                                        'language' => [
                                            'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                                        ],
                                        'ajax' => [
                                            'url' => ['../ajax/program-list'],
                                            'dataType' => 'json',
                                            'data' => new JsExpression('function(params) {return {query:params.term}; }')
                                        ],
                                        'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                                        'templateResult' => new JsExpression('function(data) {return data.text; }'),
                                        'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                                    ],
                                ])->label('Program Widget');
?>
                            </div>
                        </div>
                        <?php
                        if (!$model->isNewRecord) {
                            $selectedValueCampus = [];
                            if (!empty($campusWidData)) {
                                foreach ($campusWidData as $value) {
                                    if (!empty($value)) {
                                        $selectedValueCampus[array_keys($value)[0]] = ['selected' => true];
                                    }
                                }
                            }
                        }
                        ?>
                        <div class="row">
                            <div class="col-md-6">
                                <?= $form->field($model, 'campus_widget')->widget(Select2::classname(), [
                                    'data' => $campusWidData ?? [], //array of text to show in the field for the selected items
                                    'options' => [
                                        'placeholder' => '--Select Campus--',
                                        'multiple' => true,
                                        'options' => $selectedValueCampus ?? [],
                                    ],
                                    'pluginOptions' => [
                                        'allowClear' => true,
                                        'minimumInputLength' => 3,
                                        'language' => [
                                            'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                                        ],
                                        'ajax' => [
                                            'url' => ['../ajax/get-campus-college-list'],
                                            'dataType' => 'json',
                                            'data' => new JsExpression('function(params) {return {q:params.term, college_id:$("#customlandingpage-college_id").val()}; }')
                                        ],
                                        'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                                        'templateResult' => new JsExpression('function(data) { return data.text; }'),
                                        'templateSelection' => new JsExpression('function (data) { return data.name || $("#customlandingpage-campus_widget option[value=\'"+data.id+"\']").text(); }'),
                                    ],
                                ])->label('Campus Widget'); ?>
                            </div>

                            <?php
                            if (!$model->isNewRecord) {
                                $selectedValuePartnerCollege = [];
                                if (!empty($partnerCollegeWidData)) {
                                    foreach ($partnerCollegeWidData as $value) {
                                        if (!empty($value)) {
                                            $selectedValuePartnerCollege[array_keys($value)[0]] = ['selected' => true];
                                        }
                                    }
                                }
                            }
                            ?>
                            <div class="col-md-6">
                                <?= $form->field($model, 'partner_college_widget')->widget(Select2::classname(), [
                                    'data' => $partnerCollegeWidData ?? [],
                                    'options' => [
                                        'placeholder' => '--Select Partner College--',
                                        'multiple' => true,
                                        'options' => $selectedValuePartnerCollege ?? [],
                                    ],
                                    'pluginOptions' => [
                                        'allowClear' => true,
                                        'minimumInputLength' => 0,
                                        'language' => [
                                            'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                                        ],
                                        'ajax' => [
                                            'url' => ['../ajax/sponsored-college-list'],
                                            'dataType' => 'json',
                                            'data' => new JsExpression('function(params) {return {query:params.term, college_id:$("#customlandingpage-college_id").val()}; }')
                                        ],
                                        'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                                        'templateResult' => new JsExpression('function(data) {return data.text; }'),
                                        'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                                    ],
                                ])->label('Partner College Widget'); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <?= $form->field($model, 'form_title')->textInput(['maxlength' => true]) ?>
            </div>
            <div class="col-md-6">
                <?= $form->field($model, 'form_cta_text')->textInput(['maxlength' => true]) ?>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <?= $form->field($model, 'form_description')->textInput(['maxlength' => true]) ?>
            </div>
        </div>

        <div class="text-muted well well-sm no-shadow">
            <label> Form Fields </label>
            <div class="row">
                <div class="col-md-12">
                    <div style="max-height: 400px; overflow-y: auto; overflow-x: hidden; border: 1px solid #ccc; padding: 10px;">
                        <div class="row">
                            <?php
                            if (!$model->isNewRecord) {
                                $selectedValueStream = [];
                                if (!empty($streamData)) {
                                    foreach ($streamData as $value) {
                                        if (!empty($value)) {
                                            $selectedValueStream[array_keys($value)[0]] = ['selected' => true];
                                        }
                                    }
                                }
                            }
                            ?>
                            <div class="col-md-6">
                                <?= $form->field($model, 'stream_id')->widget(Select2::classname(), [
                                    'data' => $streamData ?? [],
                                    'options' => [
                                        'placeholder' => '--Select Stream--',
                                        'multiple' => true,
                                        'options' => $selectedValueStream ?? [],
                                    ],
                                    'pluginOptions' => [
                                        'allowClear' => true,
                                        'maximumInputLength' => 10,
                                        'language' => [
                                            'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                                        ],
                                        'ajax' => [
                                            'url' => ['../ajax/college-stream-list'],
                                            'dataType' => 'json',
                                            'data' => new JsExpression('function(params) {
                                                        return {
                                                            q: params.term, 
                                                            college_id: $("#customlandingpage-college_id").val()
                                                        }; }'),
                                        ],
                                        'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                                        'templateResult' => new JsExpression('function(data) { return data.text; }'),
                                        'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                                    ],
                                ])->label('Stream');
?>
                            </div>
                            <div class="col-md-6">
                                <?= $form->field($model, 'level_id')->widget(Select2::classname(), [
                                    'data' => ArrayHelper::map(Degree::find()
                                        ->where(['not in', 'slug', ['tenth', 'eleventh', 'twelfth', 'other']])
                                        ->all(), 'id', 'name'),
                                    'language' => 'en',
                                    'options' => [
                                        'placeholder' => '--Select Level--',
                                        'multiple' => true,
                                    ],
                                    'pluginOptions' => [
                                        'allowClear' => true
                                    ],
                                ])->label('Level'); ?>
                            </div>
                        </div>
                        <div class="row">
                            <?php
                            if (!$model->isNewRecord) {
                                $selectedValueCourse = [];
                                if (!empty($courseData)) {
                                    foreach ($courseData as $value) {
                                        if (!empty($value)) {
                                            $selectedValueCourse[array_keys($value)[0]] = ['selected' => true];
                                        }
                                    }
                                }
                            }
                            ?>
                            <div class="col-md-6">
                                <?= $form->field($model, 'course_id')->widget(Select2::classname(), [
                                    'data' => $courseData ?? [],
                                    'options' => [
                                        'placeholder' => '--Select Course--',
                                        'multiple' => true,
                                        'options' => $selectedValueCourse ?? [],
                                    ],
                                    'pluginOptions' => [
                                        'allowClear' => true,
                                        'maximumInputLength' => 10,
                                        'language' => [
                                            'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                                        ],
                                        'ajax' => [
                                            'url' => ['../ajax/get-stream-course'],
                                            'dataType' => 'json',
                                            'data' => new JsExpression('function(params) {
                                                        return {
                                                            q: params.term, 
                                                            stream_id: $("#customlandingpage-stream_id").val()
                                                        }; }'),
                                        ],
                                        'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                                        'templateResult' => new JsExpression('function(data) { return data.text; }'),
                                        'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                                    ],
                                ])->label('Course');
?>
                            </div>
                            <div class="col-md-6">
                                <?= $form->field($model, 'program_id')->widget(Select2::classname(), [
                                    'data' => ArrayHelper::map(Program::find()->select(['id', 'name'])->where(['id' => $model->program_id])->all(), 'id', 'name'),
                                    'options' => [
                                        'placeholder' => '--Select Program--',
                                        'multiple' => true,
                                    ],
                                    // 'disabled' => !$model->isNewRecord,
                                    'pluginOptions' => [
                                        'allowClear' => true,
                                        'minimumInputLength' => 1,
                                        'language' => [
                                            'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                                        ],
                                        'ajax' => [
                                            'url' => ['../ajax/get-stream-program-list'],
                                            'dataType' => 'json',
                                            'data' => new JsExpression('function(params) {
                                                        return {
                                                            q: params.term,
                                                            course_id: $("#customlandingpage-course_id").val()
                                                        }; }')
                                        ],
                                        'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                                        'templateResult' => new JsExpression('function(data) {return data.text; }'),
                                        'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                                    ],
                                ])->label('Program');
?>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <?= $form->field($model, 'city_id')->widget(Select2::classname(), [
                                    'data' => ArrayHelper::map(City::find()->all(), 'id', 'name'),
                                    'language' => 'en',
                                    'options' => [
                                        'placeholder' => '--Select City--',
                                        'multiple' => true,
                                    ],
                                    'pluginOptions' => [
                                        'allowClear' => true
                                    ],
                                ])->label('City'); ?>
                            </div>
                            <div class="col-md-6">
                                <?= $form->field($model, 'state_id')->widget(Select2::classname(), [
                                    'data' => ArrayHelper::map(State::find()->all(), 'id', 'name'),
                                    'language' => 'en',
                                    'options' => [
                                        'placeholder' => '--Select State--',
                                        'multiple' => true,
                                    ],
                                    'pluginOptions' => [
                                        'allowClear' => true
                                    ],
                                ])->label('State'); ?>
                            </div>
                        </div>
                        <?php
                        if (!$model->isNewRecord) {
                            $selectedValueCamp = [];
                            if (!empty($campData)) {
                                foreach ($campData as $value) {
                                    if (!empty($value)) {
                                        $selectedValueCamp[array_keys($value)[0]] = ['selected' => true];
                                    }
                                }
                            }
                        }
                        ?>
                        <div class="row">
                            <div class="col-md-6">
                                <?= $form->field($model, 'campus')->widget(Select2::classname(), [
                                    'data' => $campData ?? [],
                                    'options' => [
                                        'placeholder' => '--Select Campus--',
                                        'multiple' => true,
                                        'options' => $selectedValueCamp ?? [],
                                    ],
                                    'pluginOptions' => [
                                        'allowClear' => true,
                                        'minimumInputLength' => 3,
                                        'language' => [
                                            'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                                        ],
                                        'ajax' => [
                                            'url' => ['../ajax/college-list'],
                                            'dataType' => 'json',
                                            'data' => new JsExpression('function(params) {return {q:params.term}; }')
                                        ],
                                        'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                                        'templateResult' => new JsExpression('function(data) { return data.text; }'),
                                        'templateSelection' => new JsExpression('function (data) { return data.name || $("#customlandingpage-campus option[value=\'"+data.id+"\']").text(); }'),
                                    ],
                                ])->label('Campus'); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <?= $form->field($model, 'is_gmu_logo')->dropDownList(DataHelper::getConstantList('IS_GMU_LOGO', CustomLandingPage::class)); ?>
            </div>

            <div class="col-md-6">
                <?= $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', CustomLandingPage::class)); ?>
            </div>
        </div>

        <div class="box-footer">
            <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
        </div>

    </div>
    <?php ActiveForm::end(); ?>
</div>
<?php
$this->registerJs("
    function updateFcTitleOptions() {
        let selectedValues = [];

        $('.fc-title-select').each(function () {
            const val = $(this).val();
            if (val) {
                selectedValues.push(val);
            }
        });

        $('.fc-title-select').each(function () {
            const currentVal = $(this).val();
            $(this).find('option').each(function () {
                const optVal = $(this).attr('value');
                if (optVal && selectedValues.includes(optVal) && optVal !== currentVal) {
                    $(this).attr('disabled', true);
                } else {
                    $(this).attr('disabled', false);
                }
            });
        });
    }

    $(document).on('change', '.fc-title-select', function () {
        updateFcTitleOptions();
    });

    $('#custom-multiple-input-id-featured_content').on('afterAddRow', function () {
        updateFcTitleOptions();
    });

    updateFcTitleOptions();

    function toggleCollegeField() {
        var templateVal = $('#customlandingpage-template_id').val();
        $('#customlandingpage-college_id').val(null).trigger('change');
        $('#customlandingpage-stream_id').val(null).trigger('change');

        if(templateVal == 1 || templateVal == 7 || templateVal == 2 || templateVal == 3) {
            $('#customlandingpage-college_id').attr('disabled', true);
            $('#customlandingpage-stream_id').attr('multiple', false);
        } else {
            $('#customlandingpage-college_id').attr('disabled', false);
            $('#customlandingpage-stream_id').attr('multiple', true);
        }
    }
    $('#customlandingpage-template_id').on('change', toggleCollegeField);    

    ");
?>