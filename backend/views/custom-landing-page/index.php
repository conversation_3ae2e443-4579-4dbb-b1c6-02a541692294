<?php

use common\helpers\DataHelper;
use common\models\CustomLandingPage;
use common\models\CustomLandingPageLead;
use frontend\helpers\Url;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\ArrayHelper;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\CustomLandingPageSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Custom Landing Pages';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="custom-landing-page-index box box-primary">
    <div class="box-header with-border">
        <?= Html::a('Create Custom Landing Page', ['create'], ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?php // echo $this->render('_search', ['model' => $searchModel]);
        ?>
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{items}\n{summary}\n{pager}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],

                [
                    'attribute' => 'template_id',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('TEMPLATE', CustomLandingPage::class), $model->template_id);
                    },
                    'filter' => DataHelper::getConstantList('TEMPLATE', CustomLandingPage::class)
                ],
                'slug',
                [
                    'attribute' => 'college_name',
                    'label' => 'College Name',
                    'value' => function ($model) {
                        return $model->college->name ?? '';
                    }
                ],
                [
                    'attribute' => 'status',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', CustomLandingPage::class), $model->status);
                    },
                    'filter' => DataHelper::getConstantList('STATUS', CustomLandingPage::class)
                ],
                [
                    'label' => 'Lead Count',
                    'format' => 'raw',
                    'value' => function ($model) {
                        $lead_count = CustomLandingPageLead::find()->where(['slug' => $model->slug])->count();
                        return Html::a($lead_count, ['lead-count-index', 'slug' => $model->slug], [
                            'target' => '_blank',
                            'class' => 'btn btn-sm btn-outline-primary'
                        ]);
                    },
                ],
                [
                    'class' => 'yii\grid\ActionColumn',
                    'template' => '{view} {update} {custom} {clone}',
                    'buttons' => [
                        'custom' => function ($url, $model, $key) {
                            return Html::a('<i class="fas fa fa-shield"></i>', $url, [
                                'title' => 'Script Injection',
                                'aria-label' => 'Custom',
                                'data-pjax' => '0',
                            ]);
                        },
                        'clone' => function ($url, $model, $key) {
                            return Html::a('<i class="fas fa fa-copy"></i>', ['custom-landing-page/clone', 'slug' => $model->slug], [
                                'title' => 'Clone',
                                'aria-label' => 'Clone',
                                'data-pjax' => '0',
                            ]);
                        },
                    ],
                    'urlCreator' => function ($action, $model, $key, $index) {
                        if ($action === 'custom') {
                            return Url::to(['custom-landing-page/script-injection', 'id' => $model->id]);
                        }
                        return Url::to([$action, 'id' => $model->id]);
                    }
                ],
            ],
        ]); ?>
    </div>
</div>