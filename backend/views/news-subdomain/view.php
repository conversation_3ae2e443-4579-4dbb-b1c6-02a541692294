<?php

use common\helpers\DataHelper;
use common\models\News;
use common\helpers\ContentHelper;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\DetailView;
use common\models\LeadBucket;
use common\models\LeadBucketTagging;

/* @var $this yii\web\View */
/* @var $model common\models\News */

$model->name = html_entity_decode(stripslashes($model->name));
$this->title = $model->name;
$this->params['breadcrumbs'][] = ['label' => 'News Subdomain', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="news-view box box-primary">
    <div class="box-header">
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?php if (!empty($model->newsContent)): ?>
            <?= Html::a('Update News Content', ['news-content-subdomain/update', 'id' => $model->newsContent->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?php else: ?>
            <?= Html::a('Create News Content', ['news-content-subdomain/create', 'news_id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?php endif; ?>
        <?= Html::a('Live Update', ['news-subdomain-live-update/index', 'news_id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                'name',
                'display_name',
                'slug',
                [
                    'label' => 'Banner Image',
                    'attribute' => 'banner_image',
                    'format' => 'html',
                    'value' => function ($model) {
                        if (!empty($model->banner_image)) {
                            return Html::img(\Yii::$aliases['@newsImageFrontend'] . '/' . $model->banner_image, ['width' => '100', 'height' => '100', 'style' => 'margin:10px']);
                        } else {
                            return 'Banner Image is Empty.';
                        }
                    }
                ],
                [
                    'label' => 'Audio File',
                    'attribute' => 'audio',
                    'format' => 'raw',
                    'value' => function ($model) {
                        if (!empty($model->audio)) {
                            return "<audio controls='controls'>
                            <source src='" . $model->audio . "' type='audio/mpeg'>
                        </audio>";
                        }
                    }
                ],
                [
                    'label' => 'Category',
                    'attribute' => 'news_category_id',
                    'value' => !empty($model->category) ? $model->category->slug : '',
                ],
                [
                    'label' => 'Tags',
                    'format' => 'raw',
                    'value' => function ($model) {
                        return $this->render('_tags', ['model' => $model]);
                    }
                ],
                [
                    'label' => 'Language Code',
                    'attribute' => 'lang_code',
                    'format' => 'html',
                    'value' => function ($model) {
                        if (!empty($model->lang_code)) {
                            return array_search($model->lang_code, DataHelper::$languageCode);
                        }
                    }
                ],
                [
                    'label' => 'Is Popular',
                    'attribute' => 'is_popular',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('POPULAR', News::class), $model->is_popular)
                ],
                'popular_at:datetime',
                'position',
                [
                    'label' => 'Timeline',
                    'attribute' => 'expired_at',
                    'value' => function ($model) {
                        if (isset($model->expired_at) && !empty($model->expired_at)) {
                            return date('F j, Y h:i', strtotime($model->expired_at));
                        }
                    }
                ],
                [
                    'label' => 'Scheduled At',
                    'attribute' => 'scheduled_at',
                    'value' => function ($model) {
                        if (isset($model->scheduled_at) && !empty($model->scheduled_at)) {
                            return date('F j, Y h:i', strtotime($model->scheduled_at));
                        }
                    }
                ],
                [
                    'label' => 'Published At',
                    'attribute' => 'published_at',
                    'value' => function ($model) {
                        if (isset($model->published_at) && !empty($model->published_at)) {
                            return date('F j, Y h:i', strtotime($model->published_at));
                        }
                    }
                ],
                [
                    'label' => 'CTA Bucket',
                    'attribute' => 'bucket_name',
                    'value' =>  function ($model) {
                        if (!empty($model->id)) {
                            $bucketid = LeadBucketTagging::find()->select(['id', 'bucket_id'])->where(['news_id' => $model->id, 'status' => LeadBucketTagging::STATUS_ACTIVE])->one();
                            if (!empty($bucketid)) {
                                $bucketname = LeadBucket::find()->where(['id' => $bucketid->bucket_id])->active()->one();
                            }
                        }
                        return $bucketname->bucket ?? '';
                    },
                ],
                [
                    'label' => 'Status',
                    'attribute' => 'status',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('STATUS', News::class), $model->status)
                ],
                [
                    'label' => 'Stream',
                    'attribute' => 'stream_id',
                    'value' =>  $model->stream->name ?? ''
                ],
                [
                    'label' => 'Meta Title',
                    'attribute' => 'meta_title',
                    'value' =>  function ($model) {
                        if (!empty($model->newsContent)) {
                            return $model->newsContent->meta_title ?? '';
                        }
                    },
                ],
                [
                    'label' => 'Meta Description',
                    'attribute' => 'meta_description',
                    'value' =>  function ($model) {
                        if (!empty($model->newsContent)) {
                            return $model->newsContent->meta_description ?? '';
                        }
                    },
                ],[
                    'label' => 'Meta Keywords',
                    'attribute' => 'meta_keywords',
                    'value' =>  function ($model) {
                        if (!empty($model->newsContent)) {
                            return $model->newsContent->meta_keywords ?? '';
                        }
                    },
                ],[
                    'label' => 'Content',
                   'format' => 'raw',
                    'value' =>  function ($model) {
                        if (!empty($model->newsContent)) {
                            return ContentHelper::removeStyleTag(stripslashes(html_entity_decode($model->newsContent->content))) ?? '';
                        }
                    },
                ],[
                    'label' => 'h1',
                    'attribute' => 'h1',
                    'value' =>  function ($model) {
                        if (!empty($model->newsContent)) {
                            return $model->newsContent->h1 ?? '';
                        }
                    },
                ],[
                    'label' => 'Editor Remark',
                    'attribute' => 'editor_remark',
                    'value' =>  function ($model) {
                        if (!empty($model->newsContent)) {
                            return $model->newsContent->editor_remark ?? '';
                        }
                    },
                ],

                'created_at:datetime',
                'updated_at:datetime',
            ],
        ]) ?>
    </div>
</div>