<?php

use common\helpers\DataHelper;
use common\models\News;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\ArrayHelper;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\NewsSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'News';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="callout callout-info">
    <h4>Search hint!</h4>
    <p>Created At date format should be 2022-09-21</p>
</div>
<div class="news-index box box-primary">
    <div class="box-header with-border">
        <?= Html::a('Create News', ['create'], ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?php // echo $this->render('_search', ['model' => $searchModel]);
        ?>
        <?= GridView::widget([
            'dataProvider' => $dataProvider,
            'filterModel' => $searchModel,
            'layout' => "{items}\n{summary}\n{pager}",
            'columns' => [
                ['class' => 'yii\grid\SerialColumn'],

                [
                    'attribute' => 'name',
                    'label' => 'Name',
                    'value' => function ($model) {
                        return !empty($model) ? html_entity_decode(stripslashes($model->name)) : '';
                    }
                ],
                'slug',
                [
                    'attribute' => 'category',
                    'label' => 'Category',
                    'value' => function ($model) {
                        return !empty($model->category->name) ? $model->category->name : '';
                    }
                ],
                [
                    'attribute' => 'lang_code',
                    'value' => function ($model) {
                        if (!empty($model->lang_code)) {
                            return array_search($model->lang_code, DataHelper::$languageCode);
                        }
                    },
                    'filter' => array_flip(DataHelper::$languageCode)
                ],
                [
                    'attribute' => 'is_popular',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('POPULAR', News::class), $model->is_popular);
                    },
                    'filter' => DataHelper::getConstantList('POPULAR', News::class)
                ],
                // 'popular_at:date',

                [
                    'attribute' => 'popular_at',
                    'format' => 'raw',
                    'value' => function ($model) {
                        return $model->popular_at
                            ? Yii::$app->formatter->asDate($model->popular_at)
                            : '--';
                    },
                ],

                [
                    'attribute' => 'is_live',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('IS_LIVE', News::class), $model->is_live);
                    },
                    'filter' => DataHelper::getConstantList('IS_LIVE', News::class)
                ],
                'created_at:date',
                'updated_at:date',
                [
                    'attribute' => 'status',
                    'value' => function ($model) {
                        return ArrayHelper::getValue(DataHelper::getConstantList('STATUS', News::class), $model->status);
                    },
                    'filter' => DataHelper::getConstantList('STATUS', News::class)
                ],
                [
                    'class' => 'yii\grid\ActionColumn',
                    'template' => '{view} {update}',
                    // 'buttons' => [
                    //     'preview' => function ($url) {
                    //         return Html::a('<span class="glyphicon glyphicon-check"></span>', $url, ['title' => 'Preview']);
                    //     },
                    // ],
                ],
                [
                    'attribute' => 'preview',
                    'format' => 'raw',
                    'value' => function ($model) {
                        return Html::a('Preview', ['preview', 'id' => $model->id], ['target' => '_blank']);
                    },
                ],
            ],
        ]); ?>
    </div>
</div>