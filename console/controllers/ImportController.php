<?php

namespace console\controllers;

use Exception;
use common\helpers\ConsoleHelper;
use common\helpers\ContentHelper;
use common\models\CollegeNotificationUpdate;
use common\models\ExamContent;
use Yii;
use common\models\SeoInfo;
use DOMDocument;
use DOMXPath;
use Matthias<PERSON><PERSON>ie\Minify;

class ImportController extends \yii\console\Controller
{
    public function actionGenerateAssets()
    {
        self::minifyAndCopyFiles(Yii::getAlias('@frontend') . '/web/yas/css/version2/', Yii::getAlias('@frontend') . '/web/yas/css/version2/min/');
        self::minifyAndCopyFiles(Yii::getAlias('@frontend') . '/web/yas/js/version2/', Yii::getAlias('@frontend') . '/web/yas/js/version2/min/');
        echo 'Assets Generated!';
    }


    public static function minifyAndCopyFiles($sourceDir, $targetDir)
    {
        $iterator = new \RecursiveIteratorIterator(new \RecursiveDirectoryIterator($sourceDir, \RecursiveDirectoryIterator::SKIP_DOTS));

        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $filePath = $file->getPathname();
                $relativePath = str_replace($sourceDir, '', $filePath);
                $targetPath = $targetDir . $relativePath;

                if (strpos($filePath, 'min') !== false) {
                    continue;
                }

                // Determine file type (CSS or JS) and create minified version
                $extension = pathinfo($file->getFilename(), PATHINFO_EXTENSION);

                if ($extension === 'css') {
                    $minifier = new Minify\CSS($filePath);
                    $minifier->minify($targetPath);
                } elseif ($extension === 'js') {
                    $minifier = new Minify\JS($filePath);
                    $minifier->minify($targetPath);
                }
            }
        }
    }

    public function fetchGoogleDocContent($url)
    {
        try {
            $exportUrl = $this->getGoogleDocExportUrl($url);

            $client = new \GuzzleHttp\Client([
                'headers' => [
                    'User-Agent' => 'Mozilla/5.0', // Mimic a browser request
                ]
            ]);

            $response = $client->get($exportUrl, ['http_errors' => false]);

            if ($response->getStatusCode() !== 200) {
                return 'Error fetching content: HTTP ' . $response->getStatusCode();
            }

            $htmlContent = (string) $response->getBody();

            if (empty(trim($htmlContent))) {
                return 'Error: Empty response from Google Docs';
            }

            // Clean up unnecessary <p> and <span> tags
            return $this->cleanGoogleDocsHTML($htmlContent);
        } catch (\Exception $e) {
            return 'Error: ' . $e->getMessage();
        }
    }

    public function getGoogleDocExportUrl($url)
    {
        if (preg_match('/\/document\/d\/([a-zA-Z0-9_-]+)/', $url, $matches)) {
            return "https://docs.google.com/document/d/{$matches[1]}/export?format=html";
        }
        return $url; // Return original URL if no match
    }

    public function cleanGoogleDocsHTML($html)
    {
        $html = preg_replace('/<meta[^>]+>/i', '', $html); // Remove <meta> tags
        $html = preg_replace('/<script[^>]*>.*?<\/script>/is', '', $html); // Remove <script> tags
        $html = preg_replace('/<style[^>]*>.*?<\/style>/is', '', $html); // Remove <style> tags

        return $this->removeExtraPTags($html);
    }

    public function removeExtraPTags($html)
    {
        $dom = new DOMDocument();
        libxml_use_internal_errors(true); // Suppress warnings due to malformed HTML

        if (!$dom->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'))) {
            return 'Error: Unable to parse HTML.';
        }

        libxml_clear_errors();

        $xpath = new DOMXPath($dom);

        // Remove <p class="c0">
        foreach ($xpath->query('//p[contains(@class, "c0") or contains(@class, "c1")]') as $p) {
            while ($p->hasChildNodes()) {
                $p->parentNode->insertBefore($p->firstChild, $p);
            }
            $p->parentNode->removeChild($p);
        }

        // Remove <span class="c1">
        foreach ($xpath->query('//span[contains(@class, "c0") or contains(@class, "c1")]') as $span) {
            while ($span->hasChildNodes()) {
                $span->parentNode->insertBefore($span->firstChild, $span);
            }
            $span->parentNode->removeChild($span);
        }

        return $dom->saveHTML();
    }


    public function importSummaryReport($row, $failedRecords, $records, $notUpdatedRecords = [])
    {
        // Summary Report
        echo "\n==== Import Summary ====\n";
        $summaryData = [
            [
                'Total Records' => $row,
                'Updated/Inserted Records' => count($records), // Only count truly updated ones
                'Failed Records' => count($failedRecords)
            ]
        ];
        echo ConsoleHelper::table($summaryData);

        if (!empty($records)) {
            echo "\n==== Inserted Records ====\n";
            echo ConsoleHelper::table($records);
        }

        if (!empty($failedRecords)) {
            echo "\n==== Failed Records ====\n";
            echo ConsoleHelper::table($failedRecords);
        }

        if (!empty($notUpdatedRecords)) {
            echo "\n==== Not Updated Records ====\n";
            echo "\n==== Not Updated Records ====\n";
            $notUpdatedTable = [];

            foreach ($notUpdatedRecords as $id) {
                $notUpdatedTable[] = ['id' => $id];
            }

            echo ConsoleHelper::table($notUpdatedTable);
        }

        echo 'All done!' . PHP_EOL;
    }

    public function actionUpdateExamParentIdSeoTable()
    {
        $success = [];
        $failed = [];

        $examContent = ExamContent::find()
            ->select(['id', 'exam_id', 'slug', 'parent_id'])
            ->where(['NOT', ['parent_id' => null]])
            ->all();

        foreach ($examContent as $content) {
            $seoModel = SeoInfo::find()
                ->where(['entity' => SeoInfo::ENTITY_EXAM, 'entity_id' => $content->exam_id, 'page' => $content->slug])
                ->andWhere(['parent_id' => null])
                ->one();

            if (!$seoModel) {
                continue;
            }

            $seoModel->parent_id = $content->parent_id;

            if ($seoModel->save()) {
                $success[] = [
                    'SeoInfo ID' => $seoModel->id,
                    'Exam ID' => $content->exam_id,
                    'Slug' => $content->slug,
                    'Parent ID Set' => $content->parent_id,
                ];
            } else {
                $failed[] = [
                    'Exam ID' => $content->exam_id,
                    'Slug' => $content->slug,
                    'Errors' => $seoModel->getErrors(),
                ];
            }
        }
        echo "\n=== Successful Updates ===\n";
        foreach ($success as $row) {
            echo "SeoInfo ID: {$row['SeoInfo ID']}, Exam ID: {$row['Exam ID']}, Slug: {$row['Slug']}, Parent ID: {$row['Parent ID Set']}\n";
        }

        echo "\n=== Failed Updates ===\n";
        foreach ($failed as $row) {
            echo "Exam ID: {$row['Exam ID']}, Slug: {$row['Slug']}, Errors: " . json_encode($row['Errors']) . "\n";
        }

        echo "\nSummary: \n";
        echo 'Total Records Processed: ' . count($examContent) . "\n";
        echo 'Successful Updates: ' . count($success) . "\n";
        echo 'Failed Updates: ' . count($failed) . "\n";
    }

    //upload data into college_notification_update table
    public function actionUpdateCollegeNotification()
    {
        $file = Yii::getAlias('@app') . DIRECTORY_SEPARATOR . 'migrations' . DIRECTORY_SEPARATOR . 'data' . DIRECTORY_SEPARATOR . 'cut_off_notification.csv';

        if (($handle = fopen($file, 'r')) === false) {
            echo "Unable to open the file \n";
            die(__FILE__);
        }

        $i = 0;
        $summary = [];

        while (($fileop = fgetcsv($handle, 2000, ',')) !== false) {
            if ($i == 0 || empty($fileop[0])) {
                $i++;
                continue;
            }

            $collegeId = trim($fileop[0]);
            $title = ContentHelper::removeStyleTag(stripslashes(html_entity_decode($fileop[1])));
            $content = preg_replace('/^<p>|<\/p>$/i', '', trim($fileop[2]));

            $pages = array_filter([trim($fileop[3]), trim($fileop[4]), trim($fileop[5])]);

            if (empty($pages)) {
                continue;
            }

            $startDate = \Carbon\Carbon::createFromFormat('m/d/Y', trim($fileop[6]))->format('Y-m-d');
            $endDate = \Carbon\Carbon::createFromFormat('m/d/Y', trim($fileop[7]))->format('Y-m-d');

            foreach ($pages as $page) {
                $subpages = [
                    'Overview' => 'info',
                    'Admission' => 'admission',
                    'Cutoff' => 'cut-off'
                ];

                $model = CollegeNotificationUpdate::find()
                    ->where(['college_id' => $collegeId])
                    ->andWhere(['sub_page' => $subpages[$page]])
                    ->one();

                if (!$model) {
                    $model = new CollegeNotificationUpdate();
                }

                $model->college_id = $collegeId;
                $model->text = $title;
                $model->content = $content;
                $model->sub_page = $subpages[$page];
                $model->start_date = $startDate;
                $model->end_date = $endDate;
                $model->publish_at = $startDate;
                $model->status = CollegeNotificationUpdate::STATUS_ACTIVE;

                if ($model->save()) {
                    echo "Saved: CollegeID: $collegeId | Page: $page\n";

                    // Add to summary
                    if (!isset($summary[$collegeId])) {
                        $summary[$collegeId] = [];
                    }
                    $summary[$collegeId][] = $page;
                } else {
                    echo "Error: CollegeID: $collegeId | Page: $page\n";
                    print_r($model->getErrors());
                }
            }

            $i++;
        }

        echo "\n--- Summary ---\n";
        foreach ($summary as $collegeId => $pages) {
            $count = count($pages);
            $pageList = implode(', ', $pages);
            echo "College ID: $collegeId | Pages: [$pageList] | Total: $count\n";
        }
    }
}
