<?php

namespace common\models;

use Carbon\Carbon;
use common\event\SitemapEvent;
use Exception;
use Yii;
use yii\db\ActiveRecord;
use yii\behaviors\SluggableBehavior;
use yii\behaviors\TimestampBehavior;
use common\models\LeadBucketTagging;
use yii\helpers\Inflector;

/**
 * This is the model class for table "news".
 *
 * @property int $id
 * @property int $news_category_id
 * @property string $name
 * @property string|null $display_name
 * @property string $slug
 * @property string|null $banner_image
 * @property int $is_popular
 * @property int $position
 * @property string|null $expired_at
 * @property string|null $scheduled_at
 * @property string|null $published_at
 * @property int $status
 * @property string|null $created_at
 * @property string|null $updated_at
 */
class NewsSubdomain extends \yii\db\ActiveRecord
{
    const STATUS_INACTIVE = 0;
    const STATUS_ACTIVE = 1;
    const STATUS_DRAFT = 2;

    const POPULAR_NO = 0;
    const POPULAR_YES = 1;

    const IS_LIVE_NO = 0;
    const IS_LIVE_YES = 1;
    const DEFAULT_LANG_CODE = 1;


    const ENTITY_NEWS = 'news';

    const SCENARIO_IMPORTER = 'importer';
    public $bucket_id;
    public $is_name_or_slug_changed = false;
   

    const IS_FREELANCER_NO = 0;
    const IS_FREELANCER_YES = 1;

    public function behaviors()
    {
        return [
            //uncomment after running the importer
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    //    ActiveRecord::EVENT_BEFORE_UPDATE => 'updated_at',
                    // We'll handle updated_at in beforeSave method to implement conditional updates
                ],
                'value' => new \yii\db\Expression('NOW()'),
            ],
            'sluggable' => [
                'class' => SluggableBehavior::class,
                'attribute' => 'name',
                'immutable' => true // chanage it true on production
            ],
            'bedezign\yii2\audit\AuditTrailBehavior'
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'news_subdomain';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['name','slug','banner_caption', 'news_category_id', 'status', 'stream_id', 'highest_qualification', 'bucket_id'], 'required', 'when' => function ($model) {
                return $model->is_freelancer == NewsSubdomain::IS_FREELANCER_NO;
            }, 'whenClient' => "function (attribute, value) {
                if($('#update-validation').val()==1){
                    return true;
                }
                return $('#newssubdomain-is_freelancer').val() == '0';
            }", 'except' => self::SCENARIO_IMPORTER],
            [['banner_image'], 'required', 'on' => 'create'],
            [['status'], 'validatePublishedAt'],
            [['news_category_id', 'is_popular', 'is_live', 'position', 'lang_code', 'is_freelancer'], 'integer'],
            [['expired_at', 'scheduled_at', 'published_at', 'created_at', 'updated_at', 'popular_at'], 'safe'],
            [['audio'], 'file', 'skipOnEmpty' => true, 'skipOnError' => false, 'extensions' => 'mp3',  'maxSize' => 1024 * 1024 * 10, 'message' => 'Audio file only support MP3'],
            [['banner_image'], 'file', 'skipOnEmpty' => true, 'skipOnError' => false, 'extensions' => 'webp'],
            [['banner_image'], 'image', 'maxWidth' => '1200', 'maxHeight' => '667', 'maxSize' => 1024 * 100, 'extensions' => 'webp', 'message' => 'Image size should not be greater than 100kb'],
            [['name', 'display_name', 'slug', 'banner_image', 'banner_caption'], 'string', 'max' => 255],
            [['slug', 'lang_code'], 'unique', 'targetAttribute' => ['slug', 'lang_code']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'news_category_id' => 'News Category ID',
            'name' => 'Name',
            'display_name' => 'Display Name',
            'slug' => 'Slug',
            'banner_image' => 'Banner Image',
            'banner_caption' => 'Banner Caption',
            'lang_code' => 'Language Code',
            'is_popular' => 'Is Popular',
            'is_live' => 'Is Live',
            'position' => 'Position',
            'expired_at' => 'Expired At',
            'scheduled_at' => 'Scheduled At',
            'published_at' => 'Published At',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'highest_qualification' => 'Highest Qualification',
            'stream_id' => 'stream',
            'is_freelancer' => 'Is Freelancer',
            'popular_at' => 'Popular At',
        ];
    }

    /**
     * Gets query for [[News Category]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCategory()
    {
        return $this->hasOne(NewsCategory::className(), ['id' => 'news_category_id']);
    }

    /**
     * Gets query for [[News Content]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getNewsContent()
    {
        return $this->hasOne(NewsContentSubdomain::className(), ['news_id' => 'id'])->select(['id', 'news_id', 'content', 'author_id', 'h1', 'meta_title', 'meta_description', 'content', 'meta_keywords', 'status', 'updated_at']);
    }

    /**
     * Gets query for [[News Tag]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getTags()
    {
        return $this->hasMany(Tags::className(), ['id' => 'tags_id'])->viaTable('news_subdomain_tag', ['news_id' => 'id']);
    }

    public function saveTags(array $tagsIds = [])
    {
        if (empty($this->id)) {
            throw new Exception(Tags::class . ' id is required');
        }

        if (empty($tagsIds) || !$this->isNewRecord) {
            $this->unlinkAll('tags', true);
        }

        foreach ($tagsIds as $tagId) {
            $tagsModel = Tags::findOne($tagId);
            if (!$tagsModel) {
                continue;
            }

            $this->link('tags', $tagsModel);
        }
    }

    public function getArticle()
    {
        return $this->hasMany(Article::className(), ['id' => 'article_id'])->viaTable('article_news_subdomain', ['news_id' => 'id'])->orderBy(['updated_at' => SORT_DESC])->where(['status' => Article::STATUS_ACTIVE]);
    }

    public function saveArticle(array $articleIds = [])
    {
        if (empty($this->id)) {
            throw new Exception(Article::class . ' id is required');
        }

        if (empty($articleIds) || !$this->isNewRecord) {
            $this->unlinkAll('article', true);
        }

        foreach ($articleIds as $articleId) {
            $articleModel = Article::findOne($articleId);
            if (!$articleModel) {
                continue;
            }

            $this->link('article', $articleModel);
        }
    }

    /**
     * Gets query for [[News]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\ExamQuery
     */
    public function getNews()
    {
        return $this->hasMany(NewsSubdomain::className(), ['id' => 'news_id'])->viaTable('news_news_subdomain', ['news_id_map' => 'id'])->orderBy(['updated_at' => SORT_DESC])->where(['status' => News::STATUS_ACTIVE]);
    }

    public function saveNews(array $newsIds = [])
    {
        if (empty($this->id)) {
            throw new Exception(NewsSubdomain::class . ' id is required');
        }

        if (empty($newsIds) || !$this->isNewRecord) {
            $this->unlinkAll('news', true);
        }

        foreach ($newsIds as $newsId) {
            $newsModel = NewsSubdomain::findOne($newsId);
            if (!$newsModel) {
                continue;
            }

            $this->link('news', $newsModel);
        }
    }


    public function getCollege()
    {
        return $this->hasMany(College::className(), ['id' => 'college_id'])->viaTable('college_news_subdomain', ['news_id' => 'id'])->orderBy(['updated_at' => SORT_DESC]);
    }

    public function saveCollege(array $collegeIds = [])
    {
        if (empty($this->id)) {
            throw new Exception(College::class . ' id is required');
        }

        if (empty($collegeIds) || !$this->isNewRecord) {
            $this->unlinkAll('college', true);
        }

        foreach ($collegeIds as $collegeId) {
            $collegeModel = College::findOne($collegeId);
            if (!$collegeModel) {
                continue;
            }

            $this->link('college', $collegeModel);
        }
    }

    /**
     * Gets query for [[Exam]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\NewsQuery
     */
    public function getExam()
    {
        return $this->hasMany(Exam::className(), ['id' => 'exam_id'])->viaTable('exam_news_subdomain', ['news_id' => 'id'])->orderBy(['updated_at' => SORT_DESC]);
    }

    public function saveExam(array $examIds = [])
    {
        if (empty($this->id)) {
            throw new Exception(Exam::class . ' id is required');
        }

        if (empty($examIds) || !$this->isNewRecord) {
            $this->unlinkAll('exam', true);
        }

        foreach ($examIds as $examId) {
            $examModel = Exam::findOne($examId);
            if (!$examModel) {
                continue;
            }

            $this->link('exam', $examModel);
        }
    }

    public function getBoard()
    {
        return $this->hasMany(Board::className(), ['id' => 'board_id'])->viaTable('board_news_subdomain', ['news_id' => 'id'])->orderBy(['updated_at' => SORT_DESC]);
    }

    public function saveBoard(array $boardIds = [])
    {
        if (empty($this->id)) {
            throw new Exception(Board::class . ' id is required');
        }

        if (empty($boardIds) || !$this->isNewRecord) {
            $this->unlinkAll('board', true);
        }

        foreach ($boardIds as $boardId) {
            $boardModel = Board::findOne($boardId);
            if (!$boardModel) {
                continue;
            }

            $this->link('board', $boardModel);
        }
    }

    public function getCourse()
    {
        return $this->hasMany(Course::className(), ['id' => 'course_id'])->viaTable('course_news_subdomain', ['news_id' => 'id'])->orderBy(['updated_at' => SORT_DESC]);
    }

    public function saveCourse(array $courseIds = [])
    {
        if (empty($this->id)) {
            throw new Exception(Course::class . ' id is required');
        }

        if (empty($courseIds) || !$this->isNewRecord) {
            $this->unlinkAll('course', true);
        }

        foreach ($courseIds as $courseId) {
            $courseModel = Course::findOne($courseId);
            if (!$courseModel) {
                continue;
            }

            $this->link('course', $courseModel);
        }
    }

    /**
     * Gets query for [[Live Update]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\LiveUpdateQuery
     */
    public function getLiveUpdate()
    {
        $currentDate = Carbon::now()->toDateString();

        return $this->hasMany(LiveUpdate::className(), ['news_id' => 'id'])->select(['title', 'content', 'created_at', 'expired_at', 'news_id'])
            ->where(['live_update.status' => LiveUpdate::STATUS_ACTIVE])
            ->andWhere(['<=', 'live_update.expired_at', $currentDate])
            ->orderBy(['created_at' => SORT_DESC]);
    }
    /**
     * {@inheritdoc}
     * @return \common\models\query\NewsSubdomainQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new \common\models\query\NewsSubdomainQuery(get_called_class());
    }

    public function validatePublishedAt($attribute)
    {
        if (empty($this->published_at) && $this->status == self::STATUS_ACTIVE) {
            $currentDateTime = date('Y-m-d H:i:s');
            $this->published_at = $currentDateTime;
            return $this;
        }

        return $this;
    }

    public function getActiveTranslation()
    {
        $query1 = $this->hasMany(NewsSubdomain::className(), ['id' => 'tag_id'])->viaTable('news_subdomain_translation', ['news_id' => 'id']);
        $query2 =  $this->hasMany(NewsSubdomain::className(), ['id' => 'news_id'])->viaTable('news_subdomain_translation', ['tag_id' => 'id']);
        return (new yii\db\Query())
            ->select('*')
            ->from(
                $query1->union($query2)
            )
            ->where(['status' => self::STATUS_ACTIVE])
            ->orderBy(['updated_at' => SORT_DESC])
            ->all();
    }

    public function getTranslation()
    {
        return $this->hasMany(NewsSubdomain::className(), ['id' => 'tag_id'])->viaTable('news_subdomain_translation', ['news_id' => 'id'])->orderBy(['updated_at' => SORT_DESC]);
    }

    public function saveTranslation(array $translationIds = [])
    {
        if (empty($this->id)) {
            throw new Exception(NewsSubdomain::class . ' id is required');
        }

        if (empty($translationIds) || !$this->isNewRecord) {
            $this->unlinkAll('translation', true);
        }

        foreach ($translationIds as $translationId) {
            $translationModel = NewsSubdomain::findOne($translationId);
            if (!$translationModel) {
                continue;
            }

            $this->link('translation', $translationModel);
        }
    }

    public function getStream()
    {
        return $this->hasOne(Stream::className(), ['id' => 'stream_id'])->orderBy(['updated_at' => SORT_DESC]);
    }


    public function getDegree()
    {
        return $this->hasOne(Degree::className(), ['id' => 'highest_qualification'])->orderBy(['updated_at' => SORT_DESC]);
    }

    public function afterSave($insert, $changedAttributes)
    {
        if (!empty($this->id)) {
            LeadBucketTagging::taggingNewsArticle(NewsSubdomain::ENTITY_NEWS, $this->id, $this->bucket_id);
        }

        if (!$insert) {
            $nameChanged = array_key_exists('name', $changedAttributes);
            $slugChanged = array_key_exists('slug', $changedAttributes);

            if ($nameChanged || $slugChanged) {
                // Update `updated_at` to current time if name or slug changed
                $this->updated_at = new \yii\db\Expression('NOW()');
            } else {
                // Revert to old value if no name/slug change
                $this->updated_at = $this->getOldAttribute('updated_at');
            }
        }

        if ((int)$this->status == self::STATUS_ACTIVE) {
            // if (array_key_exists('name', $changedAttributes)) {
            (new SitemapEvent())->updateNewsUpdateXml($this->id, $this->status, $this->updated_at, $this->published_at);
            // }
        } else {
            (new SitemapEvent())->updateNewsUpdateXml($this->id, $this->status);
        }

        return parent::afterSave($insert, $changedAttributes);
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        } else {
            // Handle slug generation for draft news when name changes
            if (!$insert && $this->status == NewsSubdomain::STATUS_DRAFT && $this->isAttributeChanged('name') && $this->lang_code == 1) {
                $slug = Inflector::slug($this->name, '-');
                $news = self::find()->where(['slug' => $slug])->andWhere(['!=', 'id', $this->id])->exists();
                if ($news) {
                    $this->addError('slug', 'Slug already exists.');
                    return false; // Prevent saving if there's an error
                }
                $this->slug = $slug;
            }

            // Condition 1: In news Subdomain, only update timestamp if name or slug changes
            if (!$insert) {
                $nameChanged = $this->isAttributeChanged('name', false);
                $slugChanged = $this->isAttributeChanged('slug', false);
                $published_at = $this->isAttributeChanged('published_at', false);

                $this->is_name_or_slug_changed = $nameChanged || $slugChanged || $published_at;

                if ($this->is_name_or_slug_changed) {
                    $this->updated_at = new \yii\db\Expression('NOW()');
                } else {
                    $this->updated_at = $this->getOldAttribute('updated_at');
                }
            }

            if ((int)$this->is_popular === self::POPULAR_YES) {
                if ($this->popular_at === null) {
                    $this->popular_at = new \yii\db\Expression('NOW()');
                }
            } else {
                $this->popular_at = null;
            }

            return true;
        }
    }
}
