<?php

namespace common\models;

use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "college_notification_update".
 *
 * @property int $id
 * @property int|null $college_id
 * @property int|null $text
 * @property int|null $status
 * @property string|null $created_at
 * @property string|null $updated_at
 *
 * @property College $college
 */
class CollegeNotificationUpdate extends \yii\db\ActiveRecord
{
    const STATUS_INACTIVE = 0;
    const STATUS_ACTIVE = 1;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'college_notification_update';
    }

    public function behaviors()
    {
        // return [
        //     [
        //         'class' => TimestampBehavior::class,
        //         'attributes' => [
        //             ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
        //             ActiveRecord::EVENT_BEFORE_UPDATE => 'updated_at',
        //         ],
        //         'value' => new \yii\db\Expression('NOW()'),
        //     ],
        //     // 'bedezign\yii2\audit\AuditTrailBehavior'
        // ];
        return [
            'timestamp' => [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => 'updated_at',
                ],
                'value' => new \yii\db\Expression('NOW()'),
            ],
            // 'bedezign\yii2\audit\AuditTrailBehavior'
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['college_id', 'status'], 'integer'],
            [['text', 'content', 'sub_page'], 'string'],
            [['text', 'content', 'sub_page', 'start_date', 'college_id'], 'required'],
            [['created_at', 'updated_at', 'start_date', 'end_date', 'publish_at'], 'safe'],
            [['college_id'], 'exist', 'skipOnError' => true, 'targetClass' => College::className(), 'targetAttribute' => ['college_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'college_id' => 'College ID',
            'text' => 'Text',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[College]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCollege()
    {
        return $this->hasOne(College::className(), ['id' => 'college_id']);
    }
}
